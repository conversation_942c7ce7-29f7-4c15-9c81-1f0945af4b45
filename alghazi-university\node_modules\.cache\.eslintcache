[{"D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\index.js": "1", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\reportWebVitals.js": "2", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\App.js": "3", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\components\\Layout.js": "4", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Home.js": "5", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\About.js": "6", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Admissions.js": "7", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Courses.js": "8", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Contact.js": "9", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\components\\Header.js": "10", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\components\\Footer.js": "11", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\utils\\dataManager.js": "12", "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Admin.js": "13"}, {"size": 535, "mtime": 1754373084312, "results": "14", "hashOfConfig": "15"}, {"size": 362, "mtime": 1754373084981, "results": "16", "hashOfConfig": "15"}, {"size": 876, "mtime": 1754373962553, "results": "17", "hashOfConfig": "15"}, {"size": 336, "mtime": 1754373332162, "results": "18", "hashOfConfig": "15"}, {"size": 5225, "mtime": 1754373371210, "results": "19", "hashOfConfig": "15"}, {"size": 6307, "mtime": 1754373454762, "results": "20", "hashOfConfig": "15"}, {"size": 18523, "mtime": 1754373614562, "results": "21", "hashOfConfig": "15"}, {"size": 6560, "mtime": 1754373507104, "results": "22", "hashOfConfig": "15"}, {"size": 14209, "mtime": 1754373693899, "results": "23", "hashOfConfig": "15"}, {"size": 2787, "mtime": 1754373281219, "results": "24", "hashOfConfig": "15"}, {"size": 3099, "mtime": 1754373312135, "results": "25", "hashOfConfig": "15"}, {"size": 3048, "mtime": 1754373265599, "results": "26", "hashOfConfig": "15"}, {"size": 10360, "mtime": 1754373911520, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rruj8d", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\index.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\reportWebVitals.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\App.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\components\\Layout.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Home.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\About.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Admissions.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Courses.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Contact.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\components\\Header.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\components\\Footer.js", [], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\utils\\dataManager.js", ["67", "68", "69"], [], "D:\\vscode project do not open\\alghazi university\\alghazi-university\\src\\pages\\Admin.js", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 73, "column": 25, "nodeType": "72", "messageId": "73", "endLine": 73, "endColumn": 26, "suggestions": "74"}, {"ruleId": "70", "severity": 1, "message": "75", "line": 74, "column": 46, "nodeType": "72", "messageId": "73", "endLine": 74, "endColumn": 47, "suggestions": "76"}, {"ruleId": "70", "severity": 1, "message": "77", "line": 74, "column": 48, "nodeType": "72", "messageId": "73", "endLine": 74, "endColumn": 49, "suggestions": "78"}, "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["79", "80"], "Unnecessary escape character: \\(.", ["81", "82"], "Unnecessary escape character: \\).", ["83", "84"], {"messageId": "85", "fix": "86", "desc": "87"}, {"messageId": "88", "fix": "89", "desc": "90"}, {"messageId": "85", "fix": "91", "desc": "87"}, {"messageId": "88", "fix": "92", "desc": "90"}, {"messageId": "85", "fix": "93", "desc": "87"}, {"messageId": "88", "fix": "94", "desc": "90"}, "removeEscape", {"range": "95", "text": "96"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "97", "text": "98"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "99", "text": "96"}, {"range": "100", "text": "98"}, {"range": "101", "text": "96"}, {"range": "102", "text": "98"}, [1926, 1927], "", [1926, 1926], "\\", [1994, 1995], [1994, 1994], [1996, 1997], [1996, 1996]]