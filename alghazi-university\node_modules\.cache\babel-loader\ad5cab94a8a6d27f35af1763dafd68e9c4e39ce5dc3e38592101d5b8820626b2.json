{"ast": null, "code": "var _jsxFileName = \"D:\\\\vscode project do not open\\\\alghazi university\\\\alghazi-university\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"ALGHAZI UNIVERSITY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Empowering minds, shaping futures, and building tomorrow's leaders through excellence in education and research.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"social-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://facebook.com/alghaziUniversity\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": \"Facebook\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fab fa-facebook-f\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 14,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://twitter.com/alghaziUni\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": \"Twitter\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fab fa-twitter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://instagram.com/alghaziUniversity\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": \"Instagram\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fab fa-instagram\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://linkedin.com/school/alghazi-university\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": \"LinkedIn\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fab fa-linkedin-in\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/about\",\n                children: \"About Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/courses\",\n                children: \"Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/admissions\",\n                children: \"Admissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/contact\",\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Academic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/courses\",\n                children: \"Undergraduate Programs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/courses\",\n                children: \"Graduate Programs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#research\",\n                children: \"Research\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#library\",\n                children: \"Library\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#calendar\",\n                children: \"Academic Calendar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Contact Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-map-marker-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 18\n              }, this), \" 123 University Avenue, Riyadh 11451, Saudi Arabia\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 18\n              }, this), \" +966 11 123 4567\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-envelope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 18\n              }, this), \" <EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-globe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 18\n              }, this), \" www.alghazi.edu.sa\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\xA9 2024 Alghazi University. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#privacy\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#terms\",\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#accessibility\",\n              children: \"Accessibility\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport './Footer.css';\n\nconst Footer = () => {\n  return (\n    <footer className=\"footer\">\n      <div className=\"container\">\n        <div className=\"footer-content\">\n          <div className=\"footer-section\">\n            <h3>ALGHAZI UNIVERSITY</h3>\n            <p>Empowering minds, shaping futures, and building tomorrow's leaders through excellence in education and research.</p>\n            <div className=\"social-links\">\n              <a href=\"https://facebook.com/alghaziUniversity\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"Facebook\">\n                <i className=\"fab fa-facebook-f\"></i>\n              </a>\n              <a href=\"https://twitter.com/alghaziUni\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"Twitter\">\n                <i className=\"fab fa-twitter\"></i>\n              </a>\n              <a href=\"https://instagram.com/alghaziUniversity\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"Instagram\">\n                <i className=\"fab fa-instagram\"></i>\n              </a>\n              <a href=\"https://linkedin.com/school/alghazi-university\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"LinkedIn\">\n                <i className=\"fab fa-linkedin-in\"></i>\n              </a>\n            </div>\n          </div>\n\n          <div className=\"footer-section\">\n            <h4>Quick Links</h4>\n            <ul>\n              <li><a href=\"/\">Home</a></li>\n              <li><a href=\"/about\">About Us</a></li>\n              <li><a href=\"/courses\">Courses</a></li>\n              <li><a href=\"/admissions\">Admissions</a></li>\n              <li><a href=\"/contact\">Contact</a></li>\n            </ul>\n          </div>\n\n          <div className=\"footer-section\">\n            <h4>Academic</h4>\n            <ul>\n              <li><a href=\"/courses\">Undergraduate Programs</a></li>\n              <li><a href=\"/courses\">Graduate Programs</a></li>\n              <li><a href=\"#research\">Research</a></li>\n              <li><a href=\"#library\">Library</a></li>\n              <li><a href=\"#calendar\">Academic Calendar</a></li>\n            </ul>\n          </div>\n\n          <div className=\"footer-section\">\n            <h4>Contact Info</h4>\n            <div className=\"contact-info\">\n              <p><i className=\"fas fa-map-marker-alt\"></i> 123 University Avenue, Riyadh 11451, Saudi Arabia</p>\n              <p><i className=\"fas fa-phone\"></i> +966 11 123 4567</p>\n              <p><i className=\"fas fa-envelope\"></i> <EMAIL></p>\n              <p><i className=\"fas fa-globe\"></i> www.alghazi.edu.sa</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"footer-bottom\">\n          <div className=\"footer-bottom-content\">\n            <p>&copy; 2024 Alghazi University. All rights reserved.</p>\n            <div className=\"footer-links\">\n              <a href=\"#privacy\">Privacy Policy</a>\n              <a href=\"#terms\">Terms of Service</a>\n              <a href=\"#accessibility\">Accessibility</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BP,OAAA;YAAAG,QAAA,EAAG;UAAgH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvHP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAGQ,IAAI,EAAC,wCAAwC;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAAC,cAAW,UAAU;cAAAP,QAAA,eAC9GH,OAAA;gBAAGE,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,gCAAgC;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAAC,cAAW,SAAS;cAAAP,QAAA,eACrGH,OAAA;gBAAGE,SAAS,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,yCAAyC;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAAC,cAAW,WAAW;cAAAP,QAAA,eAChHH,OAAA;gBAAGE,SAAS,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,gDAAgD;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAAC,cAAW,UAAU;cAAAP,QAAA,eACtHH,OAAA;gBAAGE,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAAAL,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,QAAQ;gBAAAL,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtCP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,aAAa;gBAAAL,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAGE,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sDAAkD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClGP,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAGE,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAAiB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDP,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAGE,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,wBAAoB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DP,OAAA;cAAAG,QAAA,gBAAGH,OAAA;gBAAGE,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uBAAmB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BH,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCH,OAAA;YAAAG,QAAA,EAAG;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3DP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAGQ,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrCP,OAAA;cAAGQ,IAAI,EAAC,QAAQ;cAAAL,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrCP,OAAA;cAAGQ,IAAI,EAAC,gBAAgB;cAAAL,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACI,EAAA,GAtEIV,MAAM;AAwEZ,eAAeA,MAAM;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}