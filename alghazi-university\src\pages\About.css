/* About Hero Section */
.about-hero {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
  padding: 6rem 0 4rem;
}

.about-hero h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.about-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Mission & Vision Section */
.mission-vision {
  background: #f8f9fa;
}

.mission-card,
.vision-card {
  text-align: center;
  height: 100%;
}

.mission-card h2,
.vision-card h2 {
  color: #1e3c72;
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.mission-card h2::after,
.vision-card h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: #ffd700;
  border-radius: 2px;
}

.mission-card p,
.vision-card p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #555;
}

/* History Section */
.history {
  background: white;
}

.history-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.history-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 1.5rem;
}

.history-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-image img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Values Section */
.values {
  background: #f8f9fa;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.value-item {
  text-align: center;
  padding: 2rem 1.5rem;
  transition: all 0.3s ease;
}

.value-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.value-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
  font-size: 1.5rem;
}

.value-item h3 {
  color: #1e3c72;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

/* Statistics Section */
.about-stats {
  background: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stat-card {
  text-align: center;
  padding: 2.5rem 2rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 3rem;
  font-weight: bold;
  color: #1e3c72;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.stat-card p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Campus Gallery Section */
.campus-gallery {
  background: #f8f9fa;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.gallery-item {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem 1.5rem 1.5rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-overlay h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Contact Info Section */
.contact-info {
  background: white;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.contact-item {
  text-align: center;
  padding: 2.5rem 2rem;
  transition: all 0.3s ease;
}

.contact-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.8rem;
}

.contact-item h3 {
  color: #1e3c72;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-item p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
}

/* Loading Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .about-hero {
    padding: 4rem 0 3rem;
  }

  .about-hero h1 {
    font-size: 2.5rem;
  }

  .about-hero p {
    font-size: 1.1rem;
  }

  .values-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .history-image {
    order: -1;
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .about-hero h1 {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .value-item {
    padding: 1.5rem 1rem;
  }

  .contact-item {
    padding: 2rem 1.5rem;
  }
}
