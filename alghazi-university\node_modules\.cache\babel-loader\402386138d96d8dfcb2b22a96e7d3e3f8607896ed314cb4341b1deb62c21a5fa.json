{"ast": null, "code": "// Data management utilities for localStorage and JSON data\n\n// Load JSON data\nexport const loadCourses = async () => {\n  try {\n    const response = await import('../data/courses.json');\n    return response.default;\n  } catch (error) {\n    console.error('Error loading courses:', error);\n    return [];\n  }\n};\nexport const loadUniversityInfo = async () => {\n  try {\n    const response = await import('../data/universityInfo.json');\n    return response.default;\n  } catch (error) {\n    console.error('Error loading university info:', error);\n    return {};\n  }\n};\nexport const loadAdmissionInfo = async () => {\n  try {\n    const response = await import('../data/admissionInfo.json');\n    return response.default;\n  } catch (error) {\n    console.error('Error loading admission info:', error);\n    return {};\n  }\n};\n\n// LocalStorage utilities for form submissions\nexport const saveToLocalStorage = (key, data) => {\n  try {\n    const existingData = JSON.parse(localStorage.getItem(key) || '[]');\n    const newData = [...existingData, {\n      ...data,\n      id: Date.now(),\n      timestamp: new Date().toISOString()\n    }];\n    localStorage.setItem(key, JSON.stringify(newData));\n    return true;\n  } catch (error) {\n    console.error('Error saving to localStorage:', error);\n    return false;\n  }\n};\nexport const getFromLocalStorage = key => {\n  try {\n    return JSON.parse(localStorage.getItem(key) || '[]');\n  } catch (error) {\n    console.error('Error reading from localStorage:', error);\n    return [];\n  }\n};\nexport const clearLocalStorage = key => {\n  try {\n    localStorage.removeItem(key);\n    return true;\n  } catch (error) {\n    console.error('Error clearing localStorage:', error);\n    return false;\n  }\n};\n\n// Form validation utilities\nexport const validateEmail = email => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\nexport const validatePhone = phone => {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n  return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n};\nexport const validateRequired = value => {\n  return value && value.toString().trim().length > 0;\n};\n\n// Course filtering utilities\nexport const filterCourses = (courses, filters) => {\n  return courses.filter(course => {\n    const matchesDepartment = !filters.department || course.department === filters.department;\n    const matchesLevel = !filters.level || course.level === filters.level;\n    const matchesSearch = !filters.search || course.title.toLowerCase().includes(filters.search.toLowerCase()) || course.code.toLowerCase().includes(filters.search.toLowerCase()) || course.description.toLowerCase().includes(filters.search.toLowerCase());\n    return matchesDepartment && matchesLevel && matchesSearch;\n  });\n};\nexport const getDepartments = courses => {\n  const departments = [...new Set(courses.map(course => course.department))];\n  return departments.sort();\n};\nexport const getLevels = courses => {\n  const levels = [...new Set(courses.map(course => course.level))];\n  return levels.sort();\n};", "map": {"version": 3, "names": ["loadCourses", "response", "default", "error", "console", "loadUniversityInfo", "loadAdmissionInfo", "saveToLocalStorage", "key", "data", "existingData", "JSON", "parse", "localStorage", "getItem", "newData", "id", "Date", "now", "timestamp", "toISOString", "setItem", "stringify", "getFromLocalStorage", "clearLocalStorage", "removeItem", "validateEmail", "email", "emailRegex", "test", "validatePhone", "phone", "phoneRegex", "replace", "validateRequired", "value", "toString", "trim", "length", "filterCourses", "courses", "filters", "filter", "course", "matchesDepartment", "department", "matchesLevel", "level", "matchesSearch", "search", "title", "toLowerCase", "includes", "code", "description", "getDepartments", "departments", "Set", "map", "sort", "getLevels", "levels"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/utils/dataManager.js"], "sourcesContent": ["// Data management utilities for localStorage and JSON data\n\n// Load JSON data\nexport const loadCourses = async () => {\n  try {\n    const response = await import('../data/courses.json');\n    return response.default;\n  } catch (error) {\n    console.error('Error loading courses:', error);\n    return [];\n  }\n};\n\nexport const loadUniversityInfo = async () => {\n  try {\n    const response = await import('../data/universityInfo.json');\n    return response.default;\n  } catch (error) {\n    console.error('Error loading university info:', error);\n    return {};\n  }\n};\n\nexport const loadAdmissionInfo = async () => {\n  try {\n    const response = await import('../data/admissionInfo.json');\n    return response.default;\n  } catch (error) {\n    console.error('Error loading admission info:', error);\n    return {};\n  }\n};\n\n// LocalStorage utilities for form submissions\nexport const saveToLocalStorage = (key, data) => {\n  try {\n    const existingData = JSON.parse(localStorage.getItem(key) || '[]');\n    const newData = [...existingData, { ...data, id: Date.now(), timestamp: new Date().toISOString() }];\n    localStorage.setItem(key, JSON.stringify(newData));\n    return true;\n  } catch (error) {\n    console.error('Error saving to localStorage:', error);\n    return false;\n  }\n};\n\nexport const getFromLocalStorage = (key) => {\n  try {\n    return JSON.parse(localStorage.getItem(key) || '[]');\n  } catch (error) {\n    console.error('Error reading from localStorage:', error);\n    return [];\n  }\n};\n\nexport const clearLocalStorage = (key) => {\n  try {\n    localStorage.removeItem(key);\n    return true;\n  } catch (error) {\n    console.error('Error clearing localStorage:', error);\n    return false;\n  }\n};\n\n// Form validation utilities\nexport const validateEmail = (email) => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const validatePhone = (phone) => {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n  return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n};\n\nexport const validateRequired = (value) => {\n  return value && value.toString().trim().length > 0;\n};\n\n// Course filtering utilities\nexport const filterCourses = (courses, filters) => {\n  return courses.filter(course => {\n    const matchesDepartment = !filters.department || course.department === filters.department;\n    const matchesLevel = !filters.level || course.level === filters.level;\n    const matchesSearch = !filters.search || \n      course.title.toLowerCase().includes(filters.search.toLowerCase()) ||\n      course.code.toLowerCase().includes(filters.search.toLowerCase()) ||\n      course.description.toLowerCase().includes(filters.search.toLowerCase());\n    \n    return matchesDepartment && matchesLevel && matchesSearch;\n  });\n};\n\nexport const getDepartments = (courses) => {\n  const departments = [...new Set(courses.map(course => course.department))];\n  return departments.sort();\n};\n\nexport const getLevels = (courses) => {\n  const levels = [...new Set(courses.map(course => course.level))];\n  return levels.sort();\n};\n"], "mappings": "AAAA;;AAEA;AACA,OAAO,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC;IACrD,OAAOA,QAAQ,CAACC,OAAO;EACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF,MAAMJ,QAAQ,GAAG,MAAM,MAAM,CAAC,6BAA6B,CAAC;IAC5D,OAAOA,QAAQ,CAACC,OAAO;EACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAI;IACF,MAAML,QAAQ,GAAG,MAAM,MAAM,CAAC,4BAA4B,CAAC;IAC3D,OAAOA,QAAQ,CAACC,OAAO;EACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO,CAAC,CAAC;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAK;EAC/C,IAAI;IACF,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACN,GAAG,CAAC,IAAI,IAAI,CAAC;IAClE,MAAMO,OAAO,GAAG,CAAC,GAAGL,YAAY,EAAE;MAAE,GAAGD,IAAI;MAAEO,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MAAEC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;IAAE,CAAC,CAAC;IACnGP,YAAY,CAACQ,OAAO,CAACb,GAAG,EAAEG,IAAI,CAACW,SAAS,CAACP,OAAO,CAAC,CAAC;IAClD,OAAO,IAAI;EACb,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMoB,mBAAmB,GAAIf,GAAG,IAAK;EAC1C,IAAI;IACF,OAAOG,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACN,GAAG,CAAC,IAAI,IAAI,CAAC;EACtD,CAAC,CAAC,OAAOL,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMqB,iBAAiB,GAAIhB,GAAG,IAAK;EACxC,IAAI;IACFK,YAAY,CAACY,UAAU,CAACjB,GAAG,CAAC;IAC5B,OAAO,IAAI;EACb,CAAC,CAAC,OAAOL,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMuB,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,UAAU,GAAG,4BAA4B;EAC/C,OAAOA,UAAU,CAACC,IAAI,CAACF,KAAK,CAAC;AAC/B,CAAC;AAED,OAAO,MAAMG,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,UAAU,GAAG,wBAAwB;EAC3C,OAAOA,UAAU,CAACH,IAAI,CAACE,KAAK,CAACE,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;EACzC,OAAOA,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EACjD,OAAOD,OAAO,CAACE,MAAM,CAACC,MAAM,IAAI;IAC9B,MAAMC,iBAAiB,GAAG,CAACH,OAAO,CAACI,UAAU,IAAIF,MAAM,CAACE,UAAU,KAAKJ,OAAO,CAACI,UAAU;IACzF,MAAMC,YAAY,GAAG,CAACL,OAAO,CAACM,KAAK,IAAIJ,MAAM,CAACI,KAAK,KAAKN,OAAO,CAACM,KAAK;IACrE,MAAMC,aAAa,GAAG,CAACP,OAAO,CAACQ,MAAM,IACnCN,MAAM,CAACO,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACX,OAAO,CAACQ,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC,IACjER,MAAM,CAACU,IAAI,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACX,OAAO,CAACQ,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC,IAChER,MAAM,CAACW,WAAW,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACX,OAAO,CAACQ,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC;IAEzE,OAAOP,iBAAiB,IAAIE,YAAY,IAAIE,aAAa;EAC3D,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMO,cAAc,GAAIf,OAAO,IAAK;EACzC,MAAMgB,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACjB,OAAO,CAACkB,GAAG,CAACf,MAAM,IAAIA,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC;EAC1E,OAAOW,WAAW,CAACG,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED,OAAO,MAAMC,SAAS,GAAIpB,OAAO,IAAK;EACpC,MAAMqB,MAAM,GAAG,CAAC,GAAG,IAAIJ,GAAG,CAACjB,OAAO,CAACkB,GAAG,CAACf,MAAM,IAAIA,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;EAChE,OAAOc,MAAM,CAACF,IAAI,CAAC,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}