{"ast": null, "code": "var _jsxFileName = \"D:\\\\vscode project do not open\\\\alghazi university\\\\alghazi-university\\\\src\\\\pages\\\\Admissions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { loadAdmissionInfo, saveToLocalStorage, validateEmail, validatePhone, validateRequired } from '../utils/dataManager';\nimport './Admissions.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Admissions = () => {\n  _s();\n  var _admissionInfo$requir, _admissionInfo$requir2, _admissionInfo$requir3, _admissionInfo$requir4, _admissionInfo$deadli, _admissionInfo$deadli2, _admissionInfo$deadli3, _admissionInfo$tuitio, _admissionInfo$tuitio2, _admissionInfo$tuitio3, _admissionInfo$tuitio4, _admissionInfo$tuitio5, _admissionInfo$tuitio6, _admissionInfo$tuitio7, _admissionInfo$tuitio8, _admissionInfo$schola;\n  const [admissionInfo, setAdmissionInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    nationality: '',\n    address: '',\n    program: '',\n    level: '',\n    previousEducation: '',\n    gpa: '',\n    testScores: '',\n    personalStatement: '',\n    hasRecommendations: false,\n    agreeToTerms: false\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const admissionData = await loadAdmissionInfo();\n        setAdmissionInfo(admissionData);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading admission info:', error);\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'nationality', 'address', 'program', 'level', 'previousEducation', 'personalStatement'];\n    requiredFields.forEach(field => {\n      if (!validateRequired(formData[field])) {\n        newErrors[field] = 'This field is required';\n      }\n    });\n\n    // Email validation\n    if (formData.email && !validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation\n    if (formData.phone && !validatePhone(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // GPA validation\n    if (formData.gpa && (isNaN(formData.gpa) || formData.gpa < 0 || formData.gpa > 4)) {\n      newErrors.gpa = 'GPA must be a number between 0 and 4';\n    }\n\n    // Terms agreement validation\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the terms and conditions';\n    }\n\n    // Personal statement length validation\n    if (formData.personalStatement && formData.personalStatement.length < 100) {\n      newErrors.personalStatement = 'Personal statement must be at least 100 characters long';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Save to localStorage\n      const success = saveToLocalStorage('admissionApplications', formData);\n      if (success) {\n        setSubmitSuccess(true);\n        setFormData({\n          firstName: '',\n          lastName: '',\n          email: '',\n          phone: '',\n          dateOfBirth: '',\n          nationality: '',\n          address: '',\n          program: '',\n          level: '',\n          previousEducation: '',\n          gpa: '',\n          testScores: '',\n          personalStatement: '',\n          hasRecommendations: false,\n          agreeToTerms: false\n        });\n      } else {\n        throw new Error('Failed to save application');\n      }\n    } catch (error) {\n      console.error('Error submitting application:', error);\n      setErrors({\n        submit: 'Failed to submit application. Please try again.'\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading admission information...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n  if (submitSuccess) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admissions\",\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"success-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Application Submitted Successfully!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Thank you for your interest in Alghazi University. We have received your application and will review it carefully.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"You will receive a confirmation email shortly with your application reference number.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSubmitSuccess(false),\n                className: \"btn btn-primary\",\n                children: \"Submit Another Application\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/\",\n                className: \"btn btn-secondary\",\n                children: \"Return to Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admissions\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"admissions-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Admissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Begin your journey to academic excellence at Alghazi University\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"requirements section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Admission Requirements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"requirements-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"requirement-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Undergraduate Programs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: (_admissionInfo$requir = admissionInfo.requirements) === null || _admissionInfo$requir === void 0 ? void 0 : (_admissionInfo$requir2 = _admissionInfo$requir.undergraduate) === null || _admissionInfo$requir2 === void 0 ? void 0 : _admissionInfo$requir2.map((req, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: req\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"requirement-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Graduate Programs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: (_admissionInfo$requir3 = admissionInfo.requirements) === null || _admissionInfo$requir3 === void 0 ? void 0 : (_admissionInfo$requir4 = _admissionInfo$requir3.graduate) === null || _admissionInfo$requir4 === void 0 ? void 0 : _admissionInfo$requir4.map((req, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: req\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"application-form section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Application Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-container\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"admission-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Personal Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"First Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"firstName\",\n                    value: formData.firstName,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.firstName ? 'error' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this), errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.firstName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 42\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Last Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"lastName\",\n                    value: formData.lastName,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.lastName ? 'error' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this), errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.lastName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Email Address *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.email ? 'error' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 21\n                  }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Phone Number *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phone\",\n                    value: formData.phone,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.phone ? 'error' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Date of Birth *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"dateOfBirth\",\n                    value: formData.dateOfBirth,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.dateOfBirth ? 'error' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 21\n                  }, this), errors.dateOfBirth && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.dateOfBirth\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Nationality *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"nationality\",\n                    value: formData.nationality,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.nationality ? 'error' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this), errors.nationality && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.nationality\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Address *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"address\",\n                  value: formData.address,\n                  onChange: handleInputChange,\n                  className: `form-textarea ${errors.address ? 'error' : ''}`,\n                  rows: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), errors.address && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-error\",\n                  children: errors.address\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 38\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Academic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Desired Program *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"program\",\n                    value: formData.program,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.program ? 'error' : ''}`,\n                    placeholder: \"e.g., Computer Science, Business Administration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 21\n                  }, this), errors.program && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.program\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Level *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"level\",\n                    value: formData.level,\n                    onChange: handleInputChange,\n                    className: `form-select ${errors.level ? 'error' : ''}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Level\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"undergraduate\",\n                      children: \"Undergraduate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"graduate\",\n                      children: \"Graduate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this), errors.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Previous Education *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"previousEducation\",\n                    value: formData.previousEducation,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.previousEducation ? 'error' : ''}`,\n                    placeholder: \"e.g., High School Diploma, Bachelor's Degree\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), errors.previousEducation && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.previousEducation\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 50\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"GPA (if applicable)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"gpa\",\n                    value: formData.gpa,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.gpa ? 'error' : ''}`,\n                    step: \"0.01\",\n                    min: \"0\",\n                    max: \"4\",\n                    placeholder: \"e.g., 3.75\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), errors.gpa && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.gpa\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Test Scores (SAT, GRE, GMAT, etc.)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"testScores\",\n                  value: formData.testScores,\n                  onChange: handleInputChange,\n                  className: \"form-input\",\n                  placeholder: \"e.g., SAT: 1450, GRE: 320\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Personal Statement *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"personalStatement\",\n                  value: formData.personalStatement,\n                  onChange: handleInputChange,\n                  className: `form-textarea ${errors.personalStatement ? 'error' : ''}`,\n                  rows: \"6\",\n                  placeholder: \"Tell us about yourself, your goals, and why you want to study at Alghazi University (minimum 100 characters)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"character-count\",\n                  children: [formData.personalStatement.length, \" characters\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this), errors.personalStatement && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-error\",\n                  children: errors.personalStatement\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 48\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-label\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"hasRecommendations\",\n                    checked: formData.hasRecommendations,\n                    onChange: handleInputChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 21\n                  }, this), \"I have letters of recommendation ready to submit\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-label\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"agreeToTerms\",\n                    checked: formData.agreeToTerms,\n                    onChange: handleInputChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), \"I agree to the terms and conditions and privacy policy *\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), errors.agreeToTerms && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-error\",\n                  children: errors.agreeToTerms\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-error submit-error\",\n              children: errors.submit\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn btn-primary submit-btn\",\n                disabled: isSubmitting,\n                children: isSubmitting ? 'Submitting...' : 'Submit Application'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"additional-info section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Application Deadlines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Fall Semester:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), \" \", (_admissionInfo$deadli = admissionInfo.deadlines) === null || _admissionInfo$deadli === void 0 ? void 0 : _admissionInfo$deadli.fall]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Spring Semester:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), \" \", (_admissionInfo$deadli2 = admissionInfo.deadlines) === null || _admissionInfo$deadli2 === void 0 ? void 0 : _admissionInfo$deadli2.spring]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Summer Semester:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 21\n                }, this), \" \", (_admissionInfo$deadli3 = admissionInfo.deadlines) === null || _admissionInfo$deadli3 === void 0 ? void 0 : _admissionInfo$deadli3.summer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Tuition Fees\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tuition-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Undergraduate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Domestic: \", (_admissionInfo$tuitio = admissionInfo.tuition) === null || _admissionInfo$tuitio === void 0 ? void 0 : (_admissionInfo$tuitio2 = _admissionInfo$tuitio.undergraduate) === null || _admissionInfo$tuitio2 === void 0 ? void 0 : _admissionInfo$tuitio2.domestic]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"International: \", (_admissionInfo$tuitio3 = admissionInfo.tuition) === null || _admissionInfo$tuitio3 === void 0 ? void 0 : (_admissionInfo$tuitio4 = _admissionInfo$tuitio3.undergraduate) === null || _admissionInfo$tuitio4 === void 0 ? void 0 : _admissionInfo$tuitio4.international]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Graduate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Domestic: \", (_admissionInfo$tuitio5 = admissionInfo.tuition) === null || _admissionInfo$tuitio5 === void 0 ? void 0 : (_admissionInfo$tuitio6 = _admissionInfo$tuitio5.graduate) === null || _admissionInfo$tuitio6 === void 0 ? void 0 : _admissionInfo$tuitio6.domestic]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"International: \", (_admissionInfo$tuitio7 = admissionInfo.tuition) === null || _admissionInfo$tuitio7 === void 0 ? void 0 : (_admissionInfo$tuitio8 = _admissionInfo$tuitio7.graduate) === null || _admissionInfo$tuitio8 === void 0 ? void 0 : _admissionInfo$tuitio8.international]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Scholarships Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: (_admissionInfo$schola = admissionInfo.scholarships) === null || _admissionInfo$schola === void 0 ? void 0 : _admissionInfo$schola.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [scholarship.name, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), \" \", scholarship.amount, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: scholarship.criteria\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(Admissions, \"BvYxDdkQlahToCnulZY9MOwoPq8=\");\n_c = Admissions;\nexport default Admissions;\nvar _c;\n$RefreshReg$(_c, \"Admissions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "loadAdmissionInfo", "saveToLocalStorage", "validateEmail", "validatePhone", "validateRequired", "jsxDEV", "_jsxDEV", "Admissions", "_s", "_admissionInfo$requir", "_admissionInfo$requir2", "_admissionInfo$requir3", "_admissionInfo$requir4", "_admissionInfo$deadli", "_admissionInfo$deadli2", "_admissionInfo$deadli3", "_admissionInfo$tuitio", "_admissionInfo$tuitio2", "_admissionInfo$tuitio3", "_admissionInfo$tuitio4", "_admissionInfo$tuitio5", "_admissionInfo$tuitio6", "_admissionInfo$tuitio7", "_admissionInfo$tuitio8", "_admissionInfo$schola", "admissionInfo", "setAdmissionInfo", "loading", "setLoading", "formData", "setFormData", "firstName", "lastName", "email", "phone", "dateOfBirth", "nationality", "address", "program", "level", "previousEducation", "gpa", "testScores", "personalStatement", "hasRecommendations", "agreeToTerms", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "submitSuccess", "setSubmitSuccess", "fetchData", "admissionData", "error", "console", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "validateForm", "newErrors", "requiredFields", "for<PERSON>ach", "field", "isNaN", "length", "Object", "keys", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "success", "Error", "submit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "href", "requirements", "undergraduate", "map", "req", "index", "graduate", "onSubmit", "onChange", "rows", "placeholder", "step", "min", "max", "disabled", "deadlines", "fall", "spring", "summer", "tuition", "domestic", "international", "scholarships", "scholarship", "amount", "criteria", "_c", "$RefreshReg$"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/pages/Admissions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { loadAdmissionInfo, saveToLocalStorage, validateEmail, validatePhone, validateRequired } from '../utils/dataManager';\nimport './Admissions.css';\n\nconst Admissions = () => {\n  const [admissionInfo, setAdmissionInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    nationality: '',\n    address: '',\n    program: '',\n    level: '',\n    previousEducation: '',\n    gpa: '',\n    testScores: '',\n    personalStatement: '',\n    hasRecommendations: false,\n    agreeToTerms: false\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const admissionData = await loadAdmissionInfo();\n        setAdmissionInfo(admissionData);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading admission info:', error);\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'nationality', 'address', 'program', 'level', 'previousEducation', 'personalStatement'];\n    \n    requiredFields.forEach(field => {\n      if (!validateRequired(formData[field])) {\n        newErrors[field] = 'This field is required';\n      }\n    });\n\n    // Email validation\n    if (formData.email && !validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation\n    if (formData.phone && !validatePhone(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // GPA validation\n    if (formData.gpa && (isNaN(formData.gpa) || formData.gpa < 0 || formData.gpa > 4)) {\n      newErrors.gpa = 'GPA must be a number between 0 and 4';\n    }\n\n    // Terms agreement validation\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the terms and conditions';\n    }\n\n    // Personal statement length validation\n    if (formData.personalStatement && formData.personalStatement.length < 100) {\n      newErrors.personalStatement = 'Personal statement must be at least 100 characters long';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Save to localStorage\n      const success = saveToLocalStorage('admissionApplications', formData);\n      \n      if (success) {\n        setSubmitSuccess(true);\n        setFormData({\n          firstName: '',\n          lastName: '',\n          email: '',\n          phone: '',\n          dateOfBirth: '',\n          nationality: '',\n          address: '',\n          program: '',\n          level: '',\n          previousEducation: '',\n          gpa: '',\n          testScores: '',\n          personalStatement: '',\n          hasRecommendations: false,\n          agreeToTerms: false\n        });\n      } else {\n        throw new Error('Failed to save application');\n      }\n    } catch (error) {\n      console.error('Error submitting application:', error);\n      setErrors({ submit: 'Failed to submit application. Please try again.' });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading admission information...</p>\n      </div>\n    );\n  }\n\n  if (submitSuccess) {\n    return (\n      <div className=\"admissions\">\n        <section className=\"success-section\">\n          <div className=\"container\">\n            <div className=\"success-content\">\n              <div className=\"success-icon\">\n                <i className=\"fas fa-check-circle\"></i>\n              </div>\n              <h1>Application Submitted Successfully!</h1>\n              <p>Thank you for your interest in Alghazi University. We have received your application and will review it carefully.</p>\n              <p>You will receive a confirmation email shortly with your application reference number.</p>\n              <div className=\"success-actions\">\n                <button \n                  onClick={() => setSubmitSuccess(false)} \n                  className=\"btn btn-primary\"\n                >\n                  Submit Another Application\n                </button>\n                <a href=\"/\" className=\"btn btn-secondary\">Return to Home</a>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"admissions\">\n      {/* Hero Section */}\n      <section className=\"admissions-hero\">\n        <div className=\"container\">\n          <h1>Admissions</h1>\n          <p>Begin your journey to academic excellence at Alghazi University</p>\n        </div>\n      </section>\n\n      {/* Requirements Section */}\n      <section className=\"requirements section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Admission Requirements</h2>\n          <div className=\"requirements-grid\">\n            <div className=\"requirement-card card\">\n              <h3>Undergraduate Programs</h3>\n              <ul>\n                {admissionInfo.requirements?.undergraduate?.map((req, index) => (\n                  <li key={index}>{req}</li>\n                ))}\n              </ul>\n            </div>\n            <div className=\"requirement-card card\">\n              <h3>Graduate Programs</h3>\n              <ul>\n                {admissionInfo.requirements?.graduate?.map((req, index) => (\n                  <li key={index}>{req}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Application Form Section */}\n      <section className=\"application-form section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Application Form</h2>\n          <div className=\"form-container\">\n            <form onSubmit={handleSubmit} className=\"admission-form\">\n              {/* Personal Information */}\n              <div className=\"form-section\">\n                <h3>Personal Information</h3>\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">First Name *</label>\n                    <input\n                      type=\"text\"\n                      name=\"firstName\"\n                      value={formData.firstName}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.firstName ? 'error' : ''}`}\n                    />\n                    {errors.firstName && <span className=\"form-error\">{errors.firstName}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Last Name *</label>\n                    <input\n                      type=\"text\"\n                      name=\"lastName\"\n                      value={formData.lastName}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.lastName ? 'error' : ''}`}\n                    />\n                    {errors.lastName && <span className=\"form-error\">{errors.lastName}</span>}\n                  </div>\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Email Address *</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.email ? 'error' : ''}`}\n                    />\n                    {errors.email && <span className=\"form-error\">{errors.email}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Phone Number *</label>\n                    <input\n                      type=\"tel\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.phone ? 'error' : ''}`}\n                    />\n                    {errors.phone && <span className=\"form-error\">{errors.phone}</span>}\n                  </div>\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Date of Birth *</label>\n                    <input\n                      type=\"date\"\n                      name=\"dateOfBirth\"\n                      value={formData.dateOfBirth}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.dateOfBirth ? 'error' : ''}`}\n                    />\n                    {errors.dateOfBirth && <span className=\"form-error\">{errors.dateOfBirth}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Nationality *</label>\n                    <input\n                      type=\"text\"\n                      name=\"nationality\"\n                      value={formData.nationality}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.nationality ? 'error' : ''}`}\n                    />\n                    {errors.nationality && <span className=\"form-error\">{errors.nationality}</span>}\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Address *</label>\n                  <textarea\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    className={`form-textarea ${errors.address ? 'error' : ''}`}\n                    rows=\"3\"\n                  />\n                  {errors.address && <span className=\"form-error\">{errors.address}</span>}\n                </div>\n              </div>\n\n              {/* Academic Information */}\n              <div className=\"form-section\">\n                <h3>Academic Information</h3>\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Desired Program *</label>\n                    <input\n                      type=\"text\"\n                      name=\"program\"\n                      value={formData.program}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.program ? 'error' : ''}`}\n                      placeholder=\"e.g., Computer Science, Business Administration\"\n                    />\n                    {errors.program && <span className=\"form-error\">{errors.program}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Level *</label>\n                    <select\n                      name=\"level\"\n                      value={formData.level}\n                      onChange={handleInputChange}\n                      className={`form-select ${errors.level ? 'error' : ''}`}\n                    >\n                      <option value=\"\">Select Level</option>\n                      <option value=\"undergraduate\">Undergraduate</option>\n                      <option value=\"graduate\">Graduate</option>\n                    </select>\n                    {errors.level && <span className=\"form-error\">{errors.level}</span>}\n                  </div>\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Previous Education *</label>\n                    <input\n                      type=\"text\"\n                      name=\"previousEducation\"\n                      value={formData.previousEducation}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.previousEducation ? 'error' : ''}`}\n                      placeholder=\"e.g., High School Diploma, Bachelor's Degree\"\n                    />\n                    {errors.previousEducation && <span className=\"form-error\">{errors.previousEducation}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">GPA (if applicable)</label>\n                    <input\n                      type=\"number\"\n                      name=\"gpa\"\n                      value={formData.gpa}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.gpa ? 'error' : ''}`}\n                      step=\"0.01\"\n                      min=\"0\"\n                      max=\"4\"\n                      placeholder=\"e.g., 3.75\"\n                    />\n                    {errors.gpa && <span className=\"form-error\">{errors.gpa}</span>}\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Test Scores (SAT, GRE, GMAT, etc.)</label>\n                  <input\n                    type=\"text\"\n                    name=\"testScores\"\n                    value={formData.testScores}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    placeholder=\"e.g., SAT: 1450, GRE: 320\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Personal Statement *</label>\n                  <textarea\n                    name=\"personalStatement\"\n                    value={formData.personalStatement}\n                    onChange={handleInputChange}\n                    className={`form-textarea ${errors.personalStatement ? 'error' : ''}`}\n                    rows=\"6\"\n                    placeholder=\"Tell us about yourself, your goals, and why you want to study at Alghazi University (minimum 100 characters)\"\n                  />\n                  <div className=\"character-count\">\n                    {formData.personalStatement.length} characters\n                  </div>\n                  {errors.personalStatement && <span className=\"form-error\">{errors.personalStatement}</span>}\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"checkbox-label\">\n                    <input\n                      type=\"checkbox\"\n                      name=\"hasRecommendations\"\n                      checked={formData.hasRecommendations}\n                      onChange={handleInputChange}\n                    />\n                    I have letters of recommendation ready to submit\n                  </label>\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"checkbox-label\">\n                    <input\n                      type=\"checkbox\"\n                      name=\"agreeToTerms\"\n                      checked={formData.agreeToTerms}\n                      onChange={handleInputChange}\n                    />\n                    I agree to the terms and conditions and privacy policy *\n                  </label>\n                  {errors.agreeToTerms && <span className=\"form-error\">{errors.agreeToTerms}</span>}\n                </div>\n              </div>\n\n              {errors.submit && (\n                <div className=\"form-error submit-error\">{errors.submit}</div>\n              )}\n\n              <div className=\"form-actions\">\n                <button \n                  type=\"submit\" \n                  className=\"btn btn-primary submit-btn\"\n                  disabled={isSubmitting}\n                >\n                  {isSubmitting ? 'Submitting...' : 'Submit Application'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </section>\n\n      {/* Additional Info Section */}\n      <section className=\"additional-info section\">\n        <div className=\"container\">\n          <div className=\"info-grid\">\n            <div className=\"info-card card\">\n              <h3>Application Deadlines</h3>\n              <ul>\n                <li><strong>Fall Semester:</strong> {admissionInfo.deadlines?.fall}</li>\n                <li><strong>Spring Semester:</strong> {admissionInfo.deadlines?.spring}</li>\n                <li><strong>Summer Semester:</strong> {admissionInfo.deadlines?.summer}</li>\n              </ul>\n            </div>\n            <div className=\"info-card card\">\n              <h3>Tuition Fees</h3>\n              <div className=\"tuition-info\">\n                <h4>Undergraduate</h4>\n                <p>Domestic: {admissionInfo.tuition?.undergraduate?.domestic}</p>\n                <p>International: {admissionInfo.tuition?.undergraduate?.international}</p>\n                <h4>Graduate</h4>\n                <p>Domestic: {admissionInfo.tuition?.graduate?.domestic}</p>\n                <p>International: {admissionInfo.tuition?.graduate?.international}</p>\n              </div>\n            </div>\n            <div className=\"info-card card\">\n              <h3>Scholarships Available</h3>\n              <ul>\n                {admissionInfo.scholarships?.map((scholarship, index) => (\n                  <li key={index}>\n                    <strong>{scholarship.name}:</strong> {scholarship.amount}\n                    <br />\n                    <small>{scholarship.criteria}</small>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Admissions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,iBAAiB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC5H,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACvB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IACvCiC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,iBAAiB,EAAE,EAAE;IACrBC,GAAG,EAAE,EAAE;IACPC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,kBAAkB,EAAE,KAAK;IACzBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,MAAMqD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,aAAa,GAAG,MAAMrD,iBAAiB,CAAC,CAAC;QAC/C0B,gBAAgB,CAAC2B,aAAa,CAAC;QAC/BzB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD1B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDwB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/ChC,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIb,MAAM,CAACY,IAAI,CAAC,EAAE;MAChBX,SAAS,CAACgB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACL,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,MAAMC,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;IAEzKA,cAAc,CAACC,OAAO,CAACC,KAAK,IAAI;MAC9B,IAAI,CAAChE,gBAAgB,CAACyB,QAAQ,CAACuC,KAAK,CAAC,CAAC,EAAE;QACtCH,SAAS,CAACG,KAAK,CAAC,GAAG,wBAAwB;MAC7C;IACF,CAAC,CAAC;;IAEF;IACA,IAAIvC,QAAQ,CAACI,KAAK,IAAI,CAAC/B,aAAa,CAAC2B,QAAQ,CAACI,KAAK,CAAC,EAAE;MACpDgC,SAAS,CAAChC,KAAK,GAAG,oCAAoC;IACxD;;IAEA;IACA,IAAIJ,QAAQ,CAACK,KAAK,IAAI,CAAC/B,aAAa,CAAC0B,QAAQ,CAACK,KAAK,CAAC,EAAE;MACpD+B,SAAS,CAAC/B,KAAK,GAAG,mCAAmC;IACvD;;IAEA;IACA,IAAIL,QAAQ,CAACY,GAAG,KAAK4B,KAAK,CAACxC,QAAQ,CAACY,GAAG,CAAC,IAAIZ,QAAQ,CAACY,GAAG,GAAG,CAAC,IAAIZ,QAAQ,CAACY,GAAG,GAAG,CAAC,CAAC,EAAE;MACjFwB,SAAS,CAACxB,GAAG,GAAG,sCAAsC;IACxD;;IAEA;IACA,IAAI,CAACZ,QAAQ,CAACgB,YAAY,EAAE;MAC1BoB,SAAS,CAACpB,YAAY,GAAG,4CAA4C;IACvE;;IAEA;IACA,IAAIhB,QAAQ,CAACc,iBAAiB,IAAId,QAAQ,CAACc,iBAAiB,CAAC2B,MAAM,GAAG,GAAG,EAAE;MACzEL,SAAS,CAACtB,iBAAiB,GAAG,yDAAyD;IACzF;IAEAI,SAAS,CAACkB,SAAS,CAAC;IACpB,OAAOM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACK,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOhB,CAAC,IAAK;IAChCA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAElB,IAAI,CAACV,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAf,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM,IAAI0B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,OAAO,GAAG7E,kBAAkB,CAAC,uBAAuB,EAAE4B,QAAQ,CAAC;MAErE,IAAIiD,OAAO,EAAE;QACX3B,gBAAgB,CAAC,IAAI,CAAC;QACtBrB,WAAW,CAAC;UACVC,SAAS,EAAE,EAAE;UACbC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,WAAW,EAAE,EAAE;UACfC,WAAW,EAAE,EAAE;UACfC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,iBAAiB,EAAE,EAAE;UACrBC,GAAG,EAAE,EAAE;UACPC,UAAU,EAAE,EAAE;UACdC,iBAAiB,EAAE,EAAE;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIkC,KAAK,CAAC,4BAA4B,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDP,SAAS,CAAC;QAAEiC,MAAM,EAAE;MAAkD,CAAC,CAAC;IAC1E,CAAC,SAAS;MACR/B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,IAAItB,OAAO,EAAE;IACX,oBACErB,OAAA;MAAK2E,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtB5E,OAAA;QAAK2E,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvChF,OAAA;QAAA4E,QAAA,EAAG;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,IAAIpC,aAAa,EAAE;IACjB,oBACE5C,OAAA;MAAK2E,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzB5E,OAAA;QAAS2E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAClC5E,OAAA;UAAK2E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB5E,OAAA;YAAK2E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B5E,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5E,OAAA;gBAAG2E,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACNhF,OAAA;cAAA4E,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ChF,OAAA;cAAA4E,QAAA,EAAG;YAAkH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzHhF,OAAA;cAAA4E,QAAA,EAAG;YAAqF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5FhF,OAAA;cAAK2E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5E,OAAA;gBACEiF,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAAC,KAAK,CAAE;gBACvC8B,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC5B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThF,OAAA;gBAAGkF,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAK2E,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzB5E,OAAA;MAAS2E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAClC5E,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5E,OAAA;UAAA4E,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBhF,OAAA;UAAA4E,QAAA,EAAG;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhF,OAAA;MAAS2E,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvC5E,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5E,OAAA;UAAI2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDhF,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAK2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC5E,OAAA;cAAA4E,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BhF,OAAA;cAAA4E,QAAA,GAAAzE,qBAAA,GACGgB,aAAa,CAACgE,YAAY,cAAAhF,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BiF,aAAa,cAAAhF,sBAAA,uBAAzCA,sBAAA,CAA2CiF,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzDvF,OAAA;gBAAA4E,QAAA,EAAiBU;cAAG,GAAXC,KAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC5E,OAAA;cAAA4E,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BhF,OAAA;cAAA4E,QAAA,GAAAvE,sBAAA,GACGc,aAAa,CAACgE,YAAY,cAAA9E,sBAAA,wBAAAC,sBAAA,GAA1BD,sBAAA,CAA4BmF,QAAQ,cAAAlF,sBAAA,uBAApCA,sBAAA,CAAsC+E,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACpDvF,OAAA;gBAAA4E,QAAA,EAAiBU;cAAG,GAAXC,KAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhF,OAAA;MAAS2E,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3C5E,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5E,OAAA;UAAI2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnDhF,OAAA;UAAK2E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B5E,OAAA;YAAMyF,QAAQ,EAAEtB,YAAa;YAACQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAEtD5E,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5E,OAAA;gBAAA4E,QAAA,EAAI;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BhF,OAAA;gBAAK2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5E,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDhF,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXF,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAE9B,QAAQ,CAACE,SAAU;oBAC1BiE,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACf,SAAS,GAAG,OAAO,GAAG,EAAE;kBAAG;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,EACDxC,MAAM,CAACf,SAAS,iBAAIzB,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACf;kBAAS;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjDhF,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXF,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAE9B,QAAQ,CAACG,QAAS;oBACzBgE,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACd,QAAQ,GAAG,OAAO,GAAG,EAAE;kBAAG;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,EACDxC,MAAM,CAACd,QAAQ,iBAAI1B,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACd;kBAAQ;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5E,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrDhF,OAAA;oBACEsD,IAAI,EAAC,OAAO;oBACZF,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE9B,QAAQ,CAACI,KAAM;oBACtB+D,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACb,KAAK,GAAG,OAAO,GAAG,EAAE;kBAAG;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,EACDxC,MAAM,CAACb,KAAK,iBAAI3B,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACb;kBAAK;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpDhF,OAAA;oBACEsD,IAAI,EAAC,KAAK;oBACVF,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE9B,QAAQ,CAACK,KAAM;oBACtB8D,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACZ,KAAK,GAAG,OAAO,GAAG,EAAE;kBAAG;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,EACDxC,MAAM,CAACZ,KAAK,iBAAI5B,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACZ;kBAAK;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5E,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrDhF,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXF,IAAI,EAAC,aAAa;oBAClBC,KAAK,EAAE9B,QAAQ,CAACM,WAAY;oBAC5B6D,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACX,WAAW,GAAG,OAAO,GAAG,EAAE;kBAAG;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,EACDxC,MAAM,CAACX,WAAW,iBAAI7B,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACX;kBAAW;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnDhF,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXF,IAAI,EAAC,aAAa;oBAClBC,KAAK,EAAE9B,QAAQ,CAACO,WAAY;oBAC5B4D,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACV,WAAW,GAAG,OAAO,GAAG,EAAE;kBAAG;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,EACDxC,MAAM,CAACV,WAAW,iBAAI9B,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACV;kBAAW;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAO2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/ChF,OAAA;kBACEoD,IAAI,EAAC,SAAS;kBACdC,KAAK,EAAE9B,QAAQ,CAACQ,OAAQ;kBACxB2D,QAAQ,EAAExC,iBAAkB;kBAC5ByB,SAAS,EAAE,iBAAiBnC,MAAM,CAACT,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;kBAC5D4D,IAAI,EAAC;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDxC,MAAM,CAACT,OAAO,iBAAI/B,OAAA;kBAAM2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpC,MAAM,CAACT;gBAAO;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhF,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5E,OAAA;gBAAA4E,QAAA,EAAI;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BhF,OAAA;gBAAK2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5E,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvDhF,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXF,IAAI,EAAC,SAAS;oBACdC,KAAK,EAAE9B,QAAQ,CAACS,OAAQ;oBACxB0D,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACR,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;oBACzD4D,WAAW,EAAC;kBAAiD;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,EACDxC,MAAM,CAACR,OAAO,iBAAIhC,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACR;kBAAO;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7ChF,OAAA;oBACEoD,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE9B,QAAQ,CAACU,KAAM;oBACtByD,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,eAAenC,MAAM,CAACP,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;oBAAA2C,QAAA,gBAExD5E,OAAA;sBAAQqD,KAAK,EAAC,EAAE;sBAAAuB,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtChF,OAAA;sBAAQqD,KAAK,EAAC,eAAe;sBAAAuB,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpDhF,OAAA;sBAAQqD,KAAK,EAAC,UAAU;sBAAAuB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACRxC,MAAM,CAACP,KAAK,iBAAIjC,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACP;kBAAK;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5E,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1DhF,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXF,IAAI,EAAC,mBAAmB;oBACxBC,KAAK,EAAE9B,QAAQ,CAACW,iBAAkB;oBAClCwD,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACN,iBAAiB,GAAG,OAAO,GAAG,EAAE,EAAG;oBACnE0D,WAAW,EAAC;kBAA8C;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,EACDxC,MAAM,CAACN,iBAAiB,iBAAIlC,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACN;kBAAiB;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAO2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzDhF,OAAA;oBACEsD,IAAI,EAAC,QAAQ;oBACbF,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAE9B,QAAQ,CAACY,GAAI;oBACpBuD,QAAQ,EAAExC,iBAAkB;oBAC5ByB,SAAS,EAAE,cAAcnC,MAAM,CAACL,GAAG,GAAG,OAAO,GAAG,EAAE,EAAG;oBACrD0D,IAAI,EAAC,MAAM;oBACXC,GAAG,EAAC,GAAG;oBACPC,GAAG,EAAC,GAAG;oBACPH,WAAW,EAAC;kBAAY;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,EACDxC,MAAM,CAACL,GAAG,iBAAInC,OAAA;oBAAM2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpC,MAAM,CAACL;kBAAG;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAO2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEhF,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACXF,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE9B,QAAQ,CAACa,UAAW;kBAC3BsD,QAAQ,EAAExC,iBAAkB;kBAC5ByB,SAAS,EAAC,YAAY;kBACtBiB,WAAW,EAAC;gBAA2B;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAO2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DhF,OAAA;kBACEoD,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAE9B,QAAQ,CAACc,iBAAkB;kBAClCqD,QAAQ,EAAExC,iBAAkB;kBAC5ByB,SAAS,EAAE,iBAAiBnC,MAAM,CAACH,iBAAiB,GAAG,OAAO,GAAG,EAAE,EAAG;kBACtEsD,IAAI,EAAC,GAAG;kBACRC,WAAW,EAAC;gBAA8G;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3H,CAAC,eACFhF,OAAA;kBAAK2E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAC7BrD,QAAQ,CAACc,iBAAiB,CAAC2B,MAAM,EAAC,aACrC;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACLxC,MAAM,CAACH,iBAAiB,iBAAIrC,OAAA;kBAAM2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpC,MAAM,CAACH;gBAAiB;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB5E,OAAA;kBAAO2E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC/B5E,OAAA;oBACEsD,IAAI,EAAC,UAAU;oBACfF,IAAI,EAAC,oBAAoB;oBACzBG,OAAO,EAAEhC,QAAQ,CAACe,kBAAmB;oBACrCoD,QAAQ,EAAExC;kBAAkB;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,oDAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAO2E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC/B5E,OAAA;oBACEsD,IAAI,EAAC,UAAU;oBACfF,IAAI,EAAC,cAAc;oBACnBG,OAAO,EAAEhC,QAAQ,CAACgB,YAAa;oBAC/BmD,QAAQ,EAAExC;kBAAkB;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,4DAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACPxC,MAAM,CAACD,YAAY,iBAAIvC,OAAA;kBAAM2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpC,MAAM,CAACD;gBAAY;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELxC,MAAM,CAACkC,MAAM,iBACZ1E,OAAA;cAAK2E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEpC,MAAM,CAACkC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC9D,eAEDhF,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5E,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACbqB,SAAS,EAAC,4BAA4B;gBACtCqB,QAAQ,EAAEtD,YAAa;gBAAAkC,QAAA,EAEtBlC,YAAY,GAAG,eAAe,GAAG;cAAoB;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhF,OAAA;MAAS2E,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eAC1C5E,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5E,OAAA;UAAK2E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5E,OAAA;YAAK2E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5E,OAAA;cAAA4E,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAA4E,QAAA,gBAAI5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,GAAAzE,qBAAA,GAACY,aAAa,CAAC8E,SAAS,cAAA1F,qBAAA,uBAAvBA,qBAAA,CAAyB2F,IAAI;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxEhF,OAAA;gBAAA4E,QAAA,gBAAI5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,GAAAxE,sBAAA,GAACW,aAAa,CAAC8E,SAAS,cAAAzF,sBAAA,uBAAvBA,sBAAA,CAAyB2F,MAAM;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EhF,OAAA;gBAAA4E,QAAA,gBAAI5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,GAAAvE,sBAAA,GAACU,aAAa,CAAC8E,SAAS,cAAAxF,sBAAA,uBAAvBA,sBAAA,CAAyB2F,MAAM;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5E,OAAA;cAAA4E,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBhF,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5E,OAAA;gBAAA4E,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBhF,OAAA;gBAAA4E,QAAA,GAAG,YAAU,GAAAlE,qBAAA,GAACS,aAAa,CAACkF,OAAO,cAAA3F,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuB0E,aAAa,cAAAzE,sBAAA,uBAApCA,sBAAA,CAAsC2F,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEhF,OAAA;gBAAA4E,QAAA,GAAG,iBAAe,GAAAhE,sBAAA,GAACO,aAAa,CAACkF,OAAO,cAAAzF,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBwE,aAAa,cAAAvE,sBAAA,uBAApCA,sBAAA,CAAsC0F,aAAa;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3EhF,OAAA;gBAAA4E,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBhF,OAAA;gBAAA4E,QAAA,GAAG,YAAU,GAAA9D,sBAAA,GAACK,aAAa,CAACkF,OAAO,cAAAvF,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuB0E,QAAQ,cAAAzE,sBAAA,uBAA/BA,sBAAA,CAAiCuF,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DhF,OAAA;gBAAA4E,QAAA,GAAG,iBAAe,GAAA5D,sBAAA,GAACG,aAAa,CAACkF,OAAO,cAAArF,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBwE,QAAQ,cAAAvE,sBAAA,uBAA/BA,sBAAA,CAAiCsF,aAAa;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5E,OAAA;cAAA4E,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BhF,OAAA;cAAA4E,QAAA,GAAA1D,qBAAA,GACGC,aAAa,CAACqF,YAAY,cAAAtF,qBAAA,uBAA1BA,qBAAA,CAA4BmE,GAAG,CAAC,CAACoB,WAAW,EAAElB,KAAK,kBAClDvF,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAA4E,QAAA,GAAS6B,WAAW,CAACrD,IAAI,EAAC,GAAC;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACyB,WAAW,CAACC,MAAM,eACxD1G,OAAA;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNhF,OAAA;kBAAA4E,QAAA,EAAQ6B,WAAW,CAACE;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAH9BO,KAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAreID,UAAU;AAAA2G,EAAA,GAAV3G,UAAU;AAuehB,eAAeA,UAAU;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}