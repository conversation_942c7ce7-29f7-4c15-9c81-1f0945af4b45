import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { loadUniversityInfo, loadCourses } from '../utils/dataManager';
import './Home.css';

const Home = () => {
  const [universityInfo, setUniversityInfo] = useState({});
  const [featuredCourses, setFeaturedCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [uniInfo, courses] = await Promise.all([
          loadUniversityInfo(),
          loadCourses()
        ]);
        
        setUniversityInfo(uniInfo);
        // Get first 3 courses as featured
        setFeaturedCourses(courses.slice(0, 3));
        setLoading(false);
      } catch (error) {
        console.error('Error loading data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-overlay"></div>
        <div className="hero-content">
          <div className="container">
            <h1 className="hero-title">Welcome to {universityInfo.name}</h1>
            <p className="hero-subtitle">
              Empowering minds, shaping futures, and building tomorrow's leaders through excellence in education and research.
            </p>
            <div className="hero-buttons">
              <Link to="/courses" className="btn btn-primary">Explore Courses</Link>
              <Link to="/admissions" className="btn btn-secondary">Apply Now</Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section">
        <div className="container">
          <div className="stats-grid">
            <div className="stat-item">
              <h3>{universityInfo.statistics?.students}</h3>
              <p>Students</p>
            </div>
            <div className="stat-item">
              <h3>{universityInfo.statistics?.faculty}</h3>
              <p>Faculty Members</p>
            </div>
            <div className="stat-item">
              <h3>{universityInfo.statistics?.programs}</h3>
              <p>Academic Programs</p>
            </div>
            <div className="stat-item">
              <h3>{universityInfo.statistics?.countries}</h3>
              <p>Countries Represented</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="about-preview section">
        <div className="container">
          <div className="grid grid-2">
            <div className="about-content">
              <h2 className="section-title">About Alghazi University</h2>
              <p className="about-text">
                {universityInfo.history}
              </p>
              <p className="about-text">
                Our mission is to {universityInfo.mission?.toLowerCase()}
              </p>
              <Link to="/about" className="btn">Learn More</Link>
            </div>
            <div className="about-image">
              <img src="/logo192.png" alt="University Campus" />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Courses Section */}
      <section className="featured-courses section">
        <div className="container">
          <h2 className="section-title">Featured Courses</h2>
          <p className="section-subtitle">
            Discover our most popular academic programs designed to prepare you for success
          </p>
          <div className="grid grid-3">
            {featuredCourses.map(course => (
              <div key={course.id} className="course-card card">
                <div className="course-header">
                  <h3>{course.title}</h3>
                  <span className="course-code">{course.code}</span>
                </div>
                <p className="course-description">{course.description}</p>
                <div className="course-details">
                  <span className="course-credits">{course.credits} Credits</span>
                  <span className="course-department">{course.department}</span>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center">
            <Link to="/courses" className="btn">View All Courses</Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Start Your Journey?</h2>
            <p>Join thousands of students who have chosen Alghazi University for their academic excellence.</p>
            <div className="cta-buttons">
              <Link to="/admissions" className="btn btn-primary">Apply Now</Link>
              <Link to="/contact" className="btn btn-secondary">Contact Us</Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
