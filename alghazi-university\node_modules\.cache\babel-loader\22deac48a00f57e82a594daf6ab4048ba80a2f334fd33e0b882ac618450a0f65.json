{"ast": null, "code": "var _jsxFileName = \"D:\\\\vscode project do not open\\\\alghazi university\\\\alghazi-university\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const location = useLocation();\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n  };\n  const isActive = path => {\n    return location.pathname === path ? 'active' : '';\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            onClick: closeMenu,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo192.png\",\n              alt: \"Alghazi University\",\n              className: \"logo-img\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: \"ALGHAZI UNIVERSITY\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: `nav ${isMenuOpen ? 'nav-open' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: `nav-link ${isActive('/')}`,\n                onClick: closeMenu,\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                className: `nav-link ${isActive('/about')}`,\n                onClick: closeMenu,\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/courses\",\n                className: `nav-link ${isActive('/courses')}`,\n                onClick: closeMenu,\n                children: \"Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admissions\",\n                className: `nav-link ${isActive('/admissions')}`,\n                onClick: closeMenu,\n                children: \"Admissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: `nav-link ${isActive('/contact')}`,\n                onClick: closeMenu,\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"menu-toggle\",\n          onClick: toggleMenu,\n          \"aria-label\": \"Toggle menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hamburger\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hamburger\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hamburger\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"Qg2wQ3PX+cesjDveqHdrYeNjEOM=\", false, function () {\n  return [useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Header", "_s", "isMenuOpen", "setIsMenuOpen", "location", "toggleMenu", "closeMenu", "isActive", "path", "pathname", "className", "children", "to", "onClick", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/components/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport './Header.css';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const location = useLocation();\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n  };\n\n  const isActive = (path) => {\n    return location.pathname === path ? 'active' : '';\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"container\">\n        <div className=\"header-content\">\n          <div className=\"logo\">\n            <Link to=\"/\" onClick={closeMenu}>\n              <img src=\"/logo192.png\" alt=\"Alghazi University\" className=\"logo-img\" />\n              <span className=\"logo-text\">ALGHAZI UNIVERSITY</span>\n            </Link>\n          </div>\n          \n          <nav className={`nav ${isMenuOpen ? 'nav-open' : ''}`}>\n            <ul className=\"nav-list\">\n              <li className=\"nav-item\">\n                <Link \n                  to=\"/\" \n                  className={`nav-link ${isActive('/')}`}\n                  onClick={closeMenu}\n                >\n                  Home\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link \n                  to=\"/about\" \n                  className={`nav-link ${isActive('/about')}`}\n                  onClick={closeMenu}\n                >\n                  About\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link \n                  to=\"/courses\" \n                  className={`nav-link ${isActive('/courses')}`}\n                  onClick={closeMenu}\n                >\n                  Courses\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link \n                  to=\"/admissions\" \n                  className={`nav-link ${isActive('/admissions')}`}\n                  onClick={closeMenu}\n                >\n                  Admissions\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link \n                  to=\"/contact\" \n                  className={`nav-link ${isActive('/contact')}`}\n                  onClick={closeMenu}\n                >\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          <button \n            className=\"menu-toggle\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            <span className=\"hamburger\"></span>\n            <span className=\"hamburger\"></span>\n            <span className=\"hamburger\"></span>\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMS,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvBF,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACtBH,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMI,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOJ,QAAQ,CAACK,QAAQ,KAAKD,IAAI,GAAG,QAAQ,GAAG,EAAE;EACnD,CAAC;EAED,oBACET,OAAA;IAAQW,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBZ,OAAA;MAAKW,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBZ,OAAA;QAAKW,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BZ,OAAA;UAAKW,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBZ,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,GAAG;YAACC,OAAO,EAAEP,SAAU;YAAAK,QAAA,gBAC9BZ,OAAA;cAAKe,GAAG,EAAC,cAAc;cAACC,GAAG,EAAC,oBAAoB;cAACL,SAAS,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxEpB,OAAA;cAAMW,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENpB,OAAA;UAAKW,SAAS,EAAE,OAAOR,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UAAAS,QAAA,eACpDZ,OAAA;YAAIW,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACtBZ,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACH,IAAI;gBACHgB,EAAE,EAAC,GAAG;gBACNF,SAAS,EAAE,YAAYH,QAAQ,CAAC,GAAG,CAAC,EAAG;gBACvCM,OAAO,EAAEP,SAAU;gBAAAK,QAAA,EACpB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLpB,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACH,IAAI;gBACHgB,EAAE,EAAC,QAAQ;gBACXF,SAAS,EAAE,YAAYH,QAAQ,CAAC,QAAQ,CAAC,EAAG;gBAC5CM,OAAO,EAAEP,SAAU;gBAAAK,QAAA,EACpB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLpB,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACH,IAAI;gBACHgB,EAAE,EAAC,UAAU;gBACbF,SAAS,EAAE,YAAYH,QAAQ,CAAC,UAAU,CAAC,EAAG;gBAC9CM,OAAO,EAAEP,SAAU;gBAAAK,QAAA,EACpB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLpB,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACH,IAAI;gBACHgB,EAAE,EAAC,aAAa;gBAChBF,SAAS,EAAE,YAAYH,QAAQ,CAAC,aAAa,CAAC,EAAG;gBACjDM,OAAO,EAAEP,SAAU;gBAAAK,QAAA,EACpB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLpB,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACH,IAAI;gBACHgB,EAAE,EAAC,UAAU;gBACbF,SAAS,EAAE,YAAYH,QAAQ,CAAC,UAAU,CAAC,EAAG;gBAC9CM,OAAO,EAAEP,SAAU;gBAAAK,QAAA,EACpB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENpB,OAAA;UACEW,SAAS,EAAC,aAAa;UACvBG,OAAO,EAAER,UAAW;UACpB,cAAW,aAAa;UAAAM,QAAA,gBAExBZ,OAAA;YAAMW,SAAS,EAAC;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnCpB,OAAA;YAAMW,SAAS,EAAC;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnCpB,OAAA;YAAMW,SAAS,EAAC;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAClB,EAAA,CA1FID,MAAM;EAAA,QAEOH,WAAW;AAAA;AAAAuB,EAAA,GAFxBpB,MAAM;AA4FZ,eAAeA,MAAM;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}