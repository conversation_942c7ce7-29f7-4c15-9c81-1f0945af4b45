/* Admissions Hero Section */
.admissions-hero {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
  padding: 6rem 0 4rem;
}

.admissions-hero h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.admissions-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Requirements Section */
.requirements {
  background: #f8f9fa;
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.requirement-card {
  padding: 2.5rem;
}

.requirement-card h3 {
  color: #1e3c72;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.requirement-card ul {
  list-style: none;
  padding: 0;
}

.requirement-card li {
  padding: 0.8rem 0;
  border-bottom: 1px solid #e9ecef;
  position: relative;
  padding-left: 2rem;
}

.requirement-card li:last-child {
  border-bottom: none;
}

.requirement-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

/* Application Form Section */
.application-form {
  background: white;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.admission-form {
  background: white;
  padding: 3rem;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f8f9fa;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  color: #1e3c72;
  font-size: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 0.5rem;
  border-bottom: 3px solid #ffd700;
  display: inline-block;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e1e1;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #1e3c72;
  box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #dc3545;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  color: #dc3545;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  display: block;
}

.submit-error {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.character-count {
  text-align: right;
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: auto;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.form-actions {
  text-align: center;
  margin-top: 2rem;
}

.submit-btn {
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  min-width: 200px;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Success Section */
.success-section {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.success-content {
  text-align: center;
  max-width: 600px;
  padding: 3rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.success-icon {
  font-size: 4rem;
  color: #28a745;
  margin-bottom: 2rem;
}

.success-content h1 {
  color: #1e3c72;
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.success-content p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.success-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

/* Additional Info Section */
.additional-info {
  background: #f8f9fa;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.info-card {
  padding: 2.5rem;
}

.info-card h3 {
  color: #1e3c72;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.info-card ul {
  list-style: none;
  padding: 0;
}

.info-card li {
  padding: 0.8rem 0;
  border-bottom: 1px solid #e9ecef;
}

.info-card li:last-child {
  border-bottom: none;
}

.tuition-info h4 {
  color: #1e3c72;
  margin: 1rem 0 0.5rem 0;
  font-size: 1.1rem;
}

.tuition-info p {
  margin: 0.3rem 0;
  color: #666;
}

/* Loading Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .admissions-hero {
    padding: 4rem 0 3rem;
  }

  .admissions-hero h1 {
    font-size: 2.5rem;
  }

  .requirements-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .requirement-card {
    padding: 2rem;
  }

  .admission-form {
    padding: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .success-content {
    padding: 2rem;
    margin: 1rem;
  }

  .success-content h1 {
    font-size: 2rem;
  }

  .success-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .admissions-hero h1 {
    font-size: 2rem;
  }

  .admission-form {
    padding: 1.5rem;
  }

  .form-section h3 {
    font-size: 1.3rem;
  }

  .submit-btn {
    width: 100%;
    padding: 12px 20px;
  }

  .success-content {
    padding: 1.5rem;
  }

  .success-icon {
    font-size: 3rem;
  }
}
