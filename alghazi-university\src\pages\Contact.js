import React, { useState, useEffect } from 'react';
import { loadUniversityInfo, saveToLocalStorage, validateEmail, validatePhone, validateRequired } from '../utils/dataManager';
import './Contact.css';

const Contact = () => {
  const [universityInfo, setUniversityInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    inquiryType: 'general'
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const uniInfo = await loadUniversityInfo();
        setUniversityInfo(uniInfo);
        setLoading(false);
      } catch (error) {
        console.error('Error loading university info:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    const requiredFields = ['name', 'email', 'subject', 'message'];
    
    requiredFields.forEach(field => {
      if (!validateRequired(formData[field])) {
        newErrors[field] = 'This field is required';
      }
    });

    // Email validation
    if (formData.email && !validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation (optional but if provided, must be valid)
    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Message length validation
    if (formData.message && formData.message.length < 10) {
      newErrors.message = 'Message must be at least 10 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Save to localStorage
      const success = saveToLocalStorage('contactSubmissions', formData);
      
      if (success) {
        setSubmitSuccess(true);
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
          inquiryType: 'general'
        });
        
        // Hide success message after 5 seconds
        setTimeout(() => {
          setSubmitSuccess(false);
        }, 5000);
      } else {
        throw new Error('Failed to save contact form');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setErrors({ submit: 'Failed to send message. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading contact information...</p>
      </div>
    );
  }

  return (
    <div className="contact">
      {/* Hero Section */}
      <section className="contact-hero">
        <div className="container">
          <h1>Contact Us</h1>
          <p>Get in touch with Alghazi University - we're here to help</p>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="contact-info-section section">
        <div className="container">
          <div className="contact-info-grid">
            <div className="contact-info-card card">
              <div className="contact-icon">
                <i className="fas fa-map-marker-alt"></i>
              </div>
              <h3>Visit Our Campus</h3>
              <p>{universityInfo.contact?.address}</p>
              <a href="#map" className="contact-link">View on Map</a>
            </div>

            <div className="contact-info-card card">
              <div className="contact-icon">
                <i className="fas fa-phone"></i>
              </div>
              <h3>Call Us</h3>
              <p>{universityInfo.contact?.phone}</p>
              <a href={`tel:${universityInfo.contact?.phone}`} className="contact-link">Call Now</a>
            </div>

            <div className="contact-info-card card">
              <div className="contact-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <h3>Email Us</h3>
              <p>{universityInfo.contact?.email}</p>
              <a href={`mailto:${universityInfo.contact?.email}`} className="contact-link">Send Email</a>
            </div>

            <div className="contact-info-card card">
              <div className="contact-icon">
                <i className="fas fa-globe"></i>
              </div>
              <h3>Website</h3>
              <p>{universityInfo.contact?.website}</p>
              <a href={`https://${universityInfo.contact?.website}`} target="_blank" rel="noopener noreferrer" className="contact-link">Visit Website</a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="contact-form-section section">
        <div className="container">
          <div className="contact-content">
            <div className="contact-form-container">
              <h2>Send us a Message</h2>
              <p>Have a question or need more information? Fill out the form below and we'll get back to you as soon as possible.</p>
              
              {submitSuccess && (
                <div className="success-message">
                  <i className="fas fa-check-circle"></i>
                  <span>Thank you! Your message has been sent successfully. We'll get back to you soon.</span>
                </div>
              )}

              <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Full Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`form-input ${errors.name ? 'error' : ''}`}
                      placeholder="Enter your full name"
                    />
                    {errors.name && <span className="form-error">{errors.name}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`form-input ${errors.email ? 'error' : ''}`}
                      placeholder="Enter your email address"
                    />
                    {errors.email && <span className="form-error">{errors.email}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`form-input ${errors.phone ? 'error' : ''}`}
                      placeholder="Enter your phone number (optional)"
                    />
                    {errors.phone && <span className="form-error">{errors.phone}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Inquiry Type</label>
                    <select
                      name="inquiryType"
                      value={formData.inquiryType}
                      onChange={handleInputChange}
                      className="form-select"
                    >
                      <option value="general">General Information</option>
                      <option value="admissions">Admissions</option>
                      <option value="academics">Academic Programs</option>
                      <option value="financial">Financial Aid</option>
                      <option value="campus">Campus Life</option>
                      <option value="research">Research</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Subject *</label>
                  <input
                    type="text"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className={`form-input ${errors.subject ? 'error' : ''}`}
                    placeholder="Enter the subject of your inquiry"
                  />
                  {errors.subject && <span className="form-error">{errors.subject}</span>}
                </div>

                <div className="form-group">
                  <label className="form-label">Message *</label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    className={`form-textarea ${errors.message ? 'error' : ''}`}
                    rows="6"
                    placeholder="Enter your message or question here..."
                  />
                  <div className="character-count">
                    {formData.message.length} characters
                  </div>
                  {errors.message && <span className="form-error">{errors.message}</span>}
                </div>

                {errors.submit && (
                  <div className="form-error submit-error">{errors.submit}</div>
                )}

                <div className="form-actions">
                  <button 
                    type="submit" 
                    className="btn btn-primary submit-btn"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </button>
                </div>
              </form>
            </div>

            <div className="contact-sidebar">
              <div className="sidebar-card card">
                <h3>Office Hours</h3>
                <div className="office-hours">
                  <div className="hours-item">
                    <span className="day">Monday - Friday</span>
                    <span className="time">8:00 AM - 6:00 PM</span>
                  </div>
                  <div className="hours-item">
                    <span className="day">Saturday</span>
                    <span className="time">9:00 AM - 4:00 PM</span>
                  </div>
                  <div className="hours-item">
                    <span className="day">Sunday</span>
                    <span className="time">Closed</span>
                  </div>
                </div>
              </div>

              <div className="sidebar-card card">
                <h3>Quick Links</h3>
                <ul className="quick-links">
                  <li><a href="/admissions">Admissions Information</a></li>
                  <li><a href="/courses">Academic Programs</a></li>
                  <li><a href="#financial-aid">Financial Aid</a></li>
                  <li><a href="#campus-tours">Campus Tours</a></li>
                  <li><a href="#student-services">Student Services</a></li>
                </ul>
              </div>

              <div className="sidebar-card card">
                <h3>Follow Us</h3>
                <div className="social-links">
                  <a href={universityInfo.socialMedia?.facebook} target="_blank" rel="noopener noreferrer" className="social-link facebook">
                    <i className="fab fa-facebook-f"></i>
                    <span>Facebook</span>
                  </a>
                  <a href={universityInfo.socialMedia?.twitter} target="_blank" rel="noopener noreferrer" className="social-link twitter">
                    <i className="fab fa-twitter"></i>
                    <span>Twitter</span>
                  </a>
                  <a href={universityInfo.socialMedia?.instagram} target="_blank" rel="noopener noreferrer" className="social-link instagram">
                    <i className="fab fa-instagram"></i>
                    <span>Instagram</span>
                  </a>
                  <a href={universityInfo.socialMedia?.linkedin} target="_blank" rel="noopener noreferrer" className="social-link linkedin">
                    <i className="fab fa-linkedin-in"></i>
                    <span>LinkedIn</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="map-section" id="map">
        <div className="container">
          <h2 className="section-title">Find Us</h2>
          <div className="map-container">
            <div className="map-placeholder">
              <div className="map-content">
                <i className="fas fa-map-marker-alt"></i>
                <h3>Alghazi University Campus</h3>
                <p>{universityInfo.contact?.address}</p>
                <p>Interactive map would be embedded here using Google Maps or similar service</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
