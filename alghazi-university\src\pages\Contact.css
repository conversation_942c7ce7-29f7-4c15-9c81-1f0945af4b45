/* Contact Hero Section */
.contact-hero {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
  padding: 6rem 0 4rem;
}

.contact-hero h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.contact-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Contact Info Section */
.contact-info-section {
  background: #f8f9fa;
}

.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.contact-info-card {
  text-align: center;
  padding: 2.5rem 2rem;
  transition: all 0.3s ease;
}

.contact-info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.8rem;
}

.contact-info-card h3 {
  color: #1e3c72;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-info-card p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.contact-link {
  color: #1e3c72;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: #2a5298;
}

/* Contact Form Section */
.contact-form-section {
  background: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-form-container h2 {
  color: #1e3c72;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.contact-form-container p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-form {
  background: #f8f9fa;
  padding: 2.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e1e1;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #1e3c72;
  box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #dc3545;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  color: #dc3545;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  display: block;
}

.submit-error {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.character-count {
  text-align: right;
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.5rem;
}

.form-actions {
  text-align: center;
  margin-top: 2rem;
}

.submit-btn {
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  min-width: 200px;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.success-message i {
  font-size: 1.2rem;
}

/* Contact Sidebar */
.contact-sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sidebar-card {
  padding: 2rem;
}

.sidebar-card h3 {
  color: #1e3c72;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.office-hours {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid #e9ecef;
}

.hours-item:last-child {
  border-bottom: none;
}

.day {
  font-weight: 600;
  color: #333;
}

.time {
  color: #666;
}

.quick-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.quick-links li {
  margin-bottom: 0.8rem;
}

.quick-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s ease;
}

.quick-links a:hover {
  color: #1e3c72;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.8rem;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link.facebook {
  background: #f8f9fa;
  color: #1877f2;
}

.social-link.twitter {
  background: #f8f9fa;
  color: #1da1f2;
}

.social-link.instagram {
  background: #f8f9fa;
  color: #e4405f;
}

.social-link.linkedin {
  background: #f8f9fa;
  color: #0077b5;
}

.social-link:hover {
  transform: translateX(5px);
}

.social-link i {
  font-size: 1.2rem;
}

/* Map Section */
.map-section {
  background: #f8f9fa;
  padding: 4rem 0;
}

.map-container {
  margin-top: 3rem;
}

.map-placeholder {
  height: 400px;
  background: #e9ecef;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #adb5bd;
}

.map-content {
  text-align: center;
  color: #666;
}

.map-content i {
  font-size: 3rem;
  color: #1e3c72;
  margin-bottom: 1rem;
}

.map-content h3 {
  color: #1e3c72;
  margin-bottom: 1rem;
}

.map-content p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* Loading Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .contact-hero {
    padding: 4rem 0 3rem;
  }

  .contact-hero h1 {
    font-size: 2.5rem;
  }

  .contact-info-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .contact-form {
    padding: 2rem;
  }

  .sidebar-card {
    padding: 1.5rem;
  }

  .map-placeholder {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .contact-hero h1 {
    font-size: 2rem;
  }

  .contact-info-grid {
    grid-template-columns: 1fr;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .submit-btn {
    width: 100%;
    padding: 12px 20px;
  }

  .social-links {
    gap: 0.5rem;
  }

  .social-link {
    padding: 0.6rem;
  }
}
