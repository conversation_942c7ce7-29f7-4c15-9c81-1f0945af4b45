/* Admin Header */
.admin-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
  padding: 4rem 0 3rem;
}

.admin-header h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.admin-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Admin Navigation */
.admin-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 0;
}

.nav-tabs {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  color: #666;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tab-btn:hover {
  border-color: #1e3c72;
  color: #1e3c72;
}

.tab-btn.active {
  background: #1e3c72;
  border-color: #1e3c72;
  color: white;
}

.tab-btn i {
  font-size: 1.1rem;
}

/* Admin Content */
.admin-content {
  background: white;
  min-height: 60vh;
}

.data-section {
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-header h2 {
  color: #1e3c72;
  font-size: 1.8rem;
  margin: 0;
}

.btn-danger {
  background: #dc3545;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-2px);
}

.btn-danger:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-data i {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.no-data h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.no-data p {
  font-size: 1.1rem;
  max-width: 400px;
  margin: 0 auto;
}

/* Data Grid */
.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.data-card {
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.data-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #1e3c72;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f8f9fa;
}

.card-header h3 {
  color: #1e3c72;
  font-size: 1.3rem;
  margin: 0;
}

.timestamp {
  background: #f8f9fa;
  color: #666;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.info-row.full-width {
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
  min-width: 100px;
  flex-shrink: 0;
}

.value {
  color: #666;
  flex: 1;
}

.badge {
  background: #1e3c72;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
}

.statement,
.message {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  line-height: 1.6;
  color: #555;
  margin: 0;
  border-left: 4px solid #1e3c72;
}

/* Admin Statistics */
.admin-stats {
  background: #f8f9fa;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: bold;
  color: #1e3c72;
  margin: 0 0 0.5rem 0;
}

.stat-info p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* Loading Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .admin-header {
    padding: 3rem 0 2rem;
  }

  .admin-header h1 {
    font-size: 2rem;
  }

  .nav-tabs {
    flex-direction: column;
    align-items: center;
  }

  .tab-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .data-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .info-row {
    flex-direction: column;
    gap: 0.3rem;
  }

  .label {
    min-width: auto;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .admin-header h1 {
    font-size: 1.8rem;
  }

  .data-card {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-info h3 {
    font-size: 1.8rem;
  }
}
