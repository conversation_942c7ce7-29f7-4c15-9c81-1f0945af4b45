{"ast": null, "code": "var _jsxFileName = \"D:\\\\vscode project do not open\\\\alghazi university\\\\alghazi-university\\\\src\\\\pages\\\\About.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { loadUniversityInfo } from '../utils/dataManager';\nimport './About.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  var _universityInfo$value, _universityInfo$stati, _universityInfo$stati2, _universityInfo$stati3, _universityInfo$stati4, _universityInfo$campu, _universityInfo$conta, _universityInfo$conta2, _universityInfo$conta3;\n  const [universityInfo, setUniversityInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const uniInfo = await loadUniversityInfo();\n        setUniversityInfo(uniInfo);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading university info:', error);\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"about-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [\"About \", universityInfo.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Established in \", universityInfo.established, \" \\u2022 \", universityInfo.location]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"mission-vision section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mission-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Our Mission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: universityInfo.mission\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vision-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Our Vision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: universityInfo.vision\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"history section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section-title\",\n              children: \"Our History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"history-text\",\n              children: universityInfo.history\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"history-text\",\n              children: \"Over the decades, we have continuously evolved to meet the changing needs of our students and society, while maintaining our commitment to academic excellence and innovation.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-image\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo192.png\",\n              alt: \"University History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"values section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Our Core Values\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"values-grid\",\n          children: (_universityInfo$value = universityInfo.values) === null || _universityInfo$value === void 0 ? void 0 : _universityInfo$value.map((value, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-item card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-star\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"about-stats section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"By the Numbers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: (_universityInfo$stati = universityInfo.statistics) === null || _universityInfo$stati === void 0 ? void 0 : _universityInfo$stati.students\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Students Enrolled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"From undergraduate to doctoral programs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: (_universityInfo$stati2 = universityInfo.statistics) === null || _universityInfo$stati2 === void 0 ? void 0 : _universityInfo$stati2.faculty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Faculty Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Dedicated educators and researchers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: (_universityInfo$stati3 = universityInfo.statistics) === null || _universityInfo$stati3 === void 0 ? void 0 : _universityInfo$stati3.programs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Academic Programs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Across multiple disciplines and levels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: (_universityInfo$stati4 = universityInfo.statistics) === null || _universityInfo$stati4 === void 0 ? void 0 : _universityInfo$stati4.countries\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Countries Represented\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Creating a diverse global community\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"campus-gallery section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Virtual Campus Tour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Explore our beautiful campus and state-of-the-art facilities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gallery-grid\",\n          children: (_universityInfo$campu = universityInfo.campusImages) === null || _universityInfo$campu === void 0 ? void 0 : _universityInfo$campu.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gallery-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: image,\n              alt: `Campus view ${index + 1}`,\n              onError: e => {\n                e.target.src = '/logo192.png';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gallery-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Campus Facility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-info section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-item card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-map-marker-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Visit Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta = universityInfo.contact) === null || _universityInfo$conta === void 0 ? void 0 : _universityInfo$conta.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-item card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Call Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta2 = universityInfo.contact) === null || _universityInfo$conta2 === void 0 ? void 0 : _universityInfo$conta2.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-item card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-envelope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Email Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta3 = universityInfo.contact) === null || _universityInfo$conta3 === void 0 ? void 0 : _universityInfo$conta3.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"fvTrcgO+3jPlxQ8/jfbZ4GfeDFM=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "loadUniversityInfo", "jsxDEV", "_jsxDEV", "About", "_s", "_universityInfo$value", "_universityInfo$stati", "_universityInfo$stati2", "_universityInfo$stati3", "_universityInfo$stati4", "_universityInfo$campu", "_universityInfo$conta", "_universityInfo$conta2", "_universityInfo$conta3", "universityInfo", "setUniversityInfo", "loading", "setLoading", "fetchData", "uniInfo", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "established", "location", "mission", "vision", "history", "src", "alt", "values", "map", "value", "index", "statistics", "students", "faculty", "programs", "countries", "campusImages", "image", "onError", "e", "target", "contact", "address", "phone", "email", "_c", "$RefreshReg$"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/pages/About.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { loadUniversityInfo } from '../utils/dataManager';\nimport './About.css';\n\nconst About = () => {\n  const [universityInfo, setUniversityInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const uniInfo = await loadUniversityInfo();\n        setUniversityInfo(uniInfo);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading university info:', error);\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"about\">\n      {/* Hero Section */}\n      <section className=\"about-hero\">\n        <div className=\"container\">\n          <h1>About {universityInfo.name}</h1>\n          <p>Established in {universityInfo.established} • {universityInfo.location}</p>\n        </div>\n      </section>\n\n      {/* Mission & Vision Section */}\n      <section className=\"mission-vision section\">\n        <div className=\"container\">\n          <div className=\"grid grid-2\">\n            <div className=\"mission-card card\">\n              <h2>Our Mission</h2>\n              <p>{universityInfo.mission}</p>\n            </div>\n            <div className=\"vision-card card\">\n              <h2>Our Vision</h2>\n              <p>{universityInfo.vision}</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* History Section */}\n      <section className=\"history section\">\n        <div className=\"container\">\n          <div className=\"grid grid-2\">\n            <div className=\"history-content\">\n              <h2 className=\"section-title\">Our History</h2>\n              <p className=\"history-text\">{universityInfo.history}</p>\n              <p className=\"history-text\">\n                Over the decades, we have continuously evolved to meet the changing needs of our students \n                and society, while maintaining our commitment to academic excellence and innovation.\n              </p>\n            </div>\n            <div className=\"history-image\">\n              <img src=\"/logo192.png\" alt=\"University History\" />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Values Section */}\n      <section className=\"values section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Our Core Values</h2>\n          <div className=\"values-grid\">\n            {universityInfo.values?.map((value, index) => (\n              <div key={index} className=\"value-item card\">\n                <div className=\"value-icon\">\n                  <i className=\"fas fa-star\"></i>\n                </div>\n                <h3>{value}</h3>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Statistics Section */}\n      <section className=\"about-stats section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">By the Numbers</h2>\n          <div className=\"stats-grid\">\n            <div className=\"stat-card card\">\n              <div className=\"stat-number\">{universityInfo.statistics?.students}</div>\n              <div className=\"stat-label\">Students Enrolled</div>\n              <p>From undergraduate to doctoral programs</p>\n            </div>\n            <div className=\"stat-card card\">\n              <div className=\"stat-number\">{universityInfo.statistics?.faculty}</div>\n              <div className=\"stat-label\">Faculty Members</div>\n              <p>Dedicated educators and researchers</p>\n            </div>\n            <div className=\"stat-card card\">\n              <div className=\"stat-number\">{universityInfo.statistics?.programs}</div>\n              <div className=\"stat-label\">Academic Programs</div>\n              <p>Across multiple disciplines and levels</p>\n            </div>\n            <div className=\"stat-card card\">\n              <div className=\"stat-number\">{universityInfo.statistics?.countries}</div>\n              <div className=\"stat-label\">Countries Represented</div>\n              <p>Creating a diverse global community</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Campus Gallery Section */}\n      <section className=\"campus-gallery section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Virtual Campus Tour</h2>\n          <p className=\"section-subtitle\">\n            Explore our beautiful campus and state-of-the-art facilities\n          </p>\n          <div className=\"gallery-grid\">\n            {universityInfo.campusImages?.map((image, index) => (\n              <div key={index} className=\"gallery-item\">\n                <img \n                  src={image} \n                  alt={`Campus view ${index + 1}`}\n                  onError={(e) => {\n                    e.target.src = '/logo192.png';\n                  }}\n                />\n                <div className=\"gallery-overlay\">\n                  <h4>Campus Facility</h4>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Info Section */}\n      <section className=\"contact-info section\">\n        <div className=\"container\">\n          <div className=\"contact-grid\">\n            <div className=\"contact-item card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-map-marker-alt\"></i>\n              </div>\n              <h3>Visit Us</h3>\n              <p>{universityInfo.contact?.address}</p>\n            </div>\n            <div className=\"contact-item card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-phone\"></i>\n              </div>\n              <h3>Call Us</h3>\n              <p>{universityInfo.contact?.phone}</p>\n            </div>\n            <div className=\"contact-item card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-envelope\"></i>\n              </div>\n              <h3>Email Us</h3>\n              <p>{universityInfo.contact?.email}</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,OAAO,GAAG,MAAMnB,kBAAkB,CAAC,CAAC;QAC1Ce,iBAAiB,CAACI,OAAO,CAAC;QAC1BF,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,OAAO,EAAE;IACX,oBACEd,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBrB,OAAA;QAAKoB,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCzB,OAAA;QAAAqB,QAAA,EAAG;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEV;EAEA,oBACEzB,OAAA;IAAKoB,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEpBrB,OAAA;MAASoB,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC7BrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrB,OAAA;UAAAqB,QAAA,GAAI,QAAM,EAACT,cAAc,CAACc,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpCzB,OAAA;UAAAqB,QAAA,GAAG,iBAAe,EAACT,cAAc,CAACe,WAAW,EAAC,UAAG,EAACf,cAAc,CAACgB,QAAQ;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzB,OAAA;MAASoB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACzCrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAAqB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBzB,OAAA;cAAAqB,QAAA,EAAIT,cAAc,CAACiB;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrB,OAAA;cAAAqB,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBzB,OAAA;cAAAqB,QAAA,EAAIT,cAAc,CAACkB;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzB,OAAA;MAASoB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAClCrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrB,OAAA;YAAKoB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BrB,OAAA;cAAIoB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CzB,OAAA;cAAGoB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAET,cAAc,CAACmB;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDzB,OAAA;cAAGoB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAG5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BrB,OAAA;cAAKgC,GAAG,EAAC,cAAc;cAACC,GAAG,EAAC;YAAoB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzB,OAAA;MAASoB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrB,OAAA;UAAIoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDzB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAAlB,qBAAA,GACzBS,cAAc,CAACsB,MAAM,cAAA/B,qBAAA,uBAArBA,qBAAA,CAAuBgC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvCrC,OAAA;YAAiBoB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC1CrB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBrB,OAAA;gBAAGoB,SAAS,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNzB,OAAA;cAAAqB,QAAA,EAAKe;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,GAJRY,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzB,OAAA;MAASoB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrB,OAAA;UAAIoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDzB,OAAA;UAAKoB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrB,OAAA;YAAKoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAAjB,qBAAA,GAAEQ,cAAc,CAAC0B,UAAU,cAAAlC,qBAAA,uBAAzBA,qBAAA,CAA2BmC;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxEzB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDzB,OAAA;cAAAqB,QAAA,EAAG;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAAhB,sBAAA,GAAEO,cAAc,CAAC0B,UAAU,cAAAjC,sBAAA,uBAAzBA,sBAAA,CAA2BmC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEzB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjDzB,OAAA;cAAAqB,QAAA,EAAG;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAAf,sBAAA,GAAEM,cAAc,CAAC0B,UAAU,cAAAhC,sBAAA,uBAAzBA,sBAAA,CAA2BmC;YAAQ;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxEzB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDzB,OAAA;cAAAqB,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAAd,sBAAA,GAAEK,cAAc,CAAC0B,UAAU,cAAA/B,sBAAA,uBAAzBA,sBAAA,CAA2BmC;YAAS;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzEzB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDzB,OAAA;cAAAqB,QAAA,EAAG;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzB,OAAA;MAASoB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACzCrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrB,OAAA;UAAIoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDzB,OAAA;UAAGoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJzB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAAb,qBAAA,GAC1BI,cAAc,CAAC+B,YAAY,cAAAnC,qBAAA,uBAA3BA,qBAAA,CAA6B2B,GAAG,CAAC,CAACS,KAAK,EAAEP,KAAK,kBAC7CrC,OAAA;YAAiBoB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACvCrB,OAAA;cACEgC,GAAG,EAAEY,KAAM;cACXX,GAAG,EAAE,eAAeI,KAAK,GAAG,CAAC,EAAG;cAChCQ,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACf,GAAG,GAAG,cAAc;cAC/B;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFzB,OAAA;cAAKoB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BrB,OAAA;gBAAAqB,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA,GAVEY,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzB,OAAA;MAASoB,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvCrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrB,OAAA;gBAAGoB,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNzB,OAAA;cAAAqB,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBzB,OAAA;cAAAqB,QAAA,GAAAZ,qBAAA,GAAIG,cAAc,CAACoC,OAAO,cAAAvC,qBAAA,uBAAtBA,qBAAA,CAAwBwC;YAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrB,OAAA;gBAAGoB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACNzB,OAAA;cAAAqB,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBzB,OAAA;cAAAqB,QAAA,GAAAX,sBAAA,GAAIE,cAAc,CAACoC,OAAO,cAAAtC,sBAAA,uBAAtBA,sBAAA,CAAwBwC;YAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNzB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrB,OAAA;gBAAGoB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNzB,OAAA;cAAAqB,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBzB,OAAA;cAAAqB,QAAA,GAAAV,sBAAA,GAAIC,cAAc,CAACoC,OAAO,cAAArC,sBAAA,uBAAtBA,sBAAA,CAAwBwC;YAAK;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACvB,EAAA,CA/KID,KAAK;AAAAmD,EAAA,GAALnD,KAAK;AAiLX,eAAeA,KAAK;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}