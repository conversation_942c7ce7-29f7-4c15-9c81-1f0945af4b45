{"ast": null, "code": "var _jsxFileName = \"D:\\\\vscode project do not open\\\\alghazi university\\\\alghazi-university\\\\src\\\\components\\\\Layout.js\";\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport './Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Header", "Footer", "jsxDEV", "_jsxDEV", "Layout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/components/Layout.js"], "sourcesContent": ["import React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport './Layout.css';\n\nconst Layout = ({ children }) => {\n  return (\n    <div className=\"layout\">\n      <Header />\n      <main className=\"main-content\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC/B,oBACEF,OAAA;IAAKG,SAAS,EAAC,QAAQ;IAAAD,QAAA,gBACrBF,OAAA,CAACH,MAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA;MAAMG,SAAS,EAAC,cAAc;MAAAD,QAAA,EAC3BA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPP,OAAA,CAACF,MAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACC,EAAA,GAVIP,MAAM;AAYZ,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}