/* Courses Hero Section */
.courses-hero {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
  padding: 6rem 0 4rem;
}

.courses-hero h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.courses-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Filters Section */
.filters-section {
  background: #f8f9fa;
  padding: 2rem 0;
  border-bottom: 1px solid #e9ecef;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.search-filter {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  border: 2px solid #e1e1e1;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #1e3c72;
  box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

.select-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 10px 15px;
  border: 2px solid #e1e1e1;
  border-radius: 8px;
  font-size: 0.95rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #1e3c72;
}

.clear-filters-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  transition: background-color 0.3s ease;
}

.clear-filters-btn:hover {
  background: #5a6268;
}

.results-info {
  text-align: center;
  color: #666;
  font-size: 0.95rem;
}

/* Courses Grid */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.course-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-color: #1e3c72;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.course-title-section {
  flex: 1;
}

.course-title {
  color: #1e3c72;
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.course-code {
  background: #e9ecef;
  color: #495057;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
}

.course-level {
  margin-left: 1rem;
}

.level-badge {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.level-badge.undergraduate {
  background: #d4edda;
  color: #155724;
}

.level-badge.graduate {
  background: #d1ecf1;
  color: #0c5460;
}

.course-content {
  flex: 1;
  margin-bottom: 1.5rem;
}

.course-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.course-details {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.95rem;
  color: #555;
}

.detail-item i {
  color: #1e3c72;
  width: 16px;
  text-align: center;
}

.course-footer {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.btn-outline {
  background: transparent;
  border: 2px solid #1e3c72;
  color: #1e3c72;
  flex: 1;
}

.btn-outline:hover {
  background: #1e3c72;
  color: white;
}

.btn-primary {
  flex: 1;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-results h3 {
  color: #333;
  margin-bottom: 1rem;
}

.no-results p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* CTA Section */
.courses-cta {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Loading Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .courses-hero {
    padding: 4rem 0 3rem;
  }

  .courses-hero h1 {
    font-size: 2.5rem;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-filter {
    min-width: auto;
  }

  .select-filters {
    justify-content: center;
  }

  .courses-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .course-header {
    flex-direction: column;
    gap: 1rem;
  }

  .course-level {
    margin-left: 0;
    align-self: flex-start;
  }

  .course-footer {
    flex-direction: column;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .courses-hero h1 {
    font-size: 2rem;
  }

  .select-filters {
    flex-direction: column;
    width: 100%;
  }

  .filter-select,
  .clear-filters-btn {
    width: 100%;
  }

  .course-card {
    padding: 1.5rem;
  }
}
