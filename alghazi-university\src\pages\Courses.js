import React, { useState, useEffect } from 'react';
import { loadCourses, filterCourses, getDepartments, getLevels } from '../utils/dataManager';
import './Courses.css';

const Courses = () => {
  const [courses, setCourses] = useState([]);
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [levels, setLevels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: '',
    department: '',
    level: ''
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const coursesData = await loadCourses();
        setCourses(coursesData);
        setFilteredCourses(coursesData);
        setDepartments(getDepartments(coursesData));
        setLevels(getLevels(coursesData));
        setLoading(false);
      } catch (error) {
        console.error('Error loading courses:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const filtered = filterCourses(courses, filters);
    setFilteredCourses(filtered);
  }, [courses, filters]);

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      department: '',
      level: ''
    });
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading courses...</p>
      </div>
    );
  }

  return (
    <div className="courses">
      {/* Hero Section */}
      <section className="courses-hero">
        <div className="container">
          <h1>Our Academic Programs</h1>
          <p>Discover world-class education across diverse fields of study</p>
        </div>
      </section>

      {/* Filters Section */}
      <section className="filters-section">
        <div className="container">
          <div className="filters-container">
            <div className="search-filter">
              <input
                type="text"
                placeholder="Search courses..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="select-filters">
              <select
                value={filters.department}
                onChange={(e) => handleFilterChange('department', e.target.value)}
                className="filter-select"
              >
                <option value="">All Departments</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>

              <select
                value={filters.level}
                onChange={(e) => handleFilterChange('level', e.target.value)}
                className="filter-select"
              >
                <option value="">All Levels</option>
                {levels.map(level => (
                  <option key={level} value={level}>{level}</option>
                ))}
              </select>

              <button onClick={clearFilters} className="clear-filters-btn">
                Clear Filters
              </button>
            </div>
          </div>

          <div className="results-info">
            <p>Showing {filteredCourses.length} of {courses.length} courses</p>
          </div>
        </div>
      </section>

      {/* Courses Grid Section */}
      <section className="courses-grid-section section">
        <div className="container">
          {filteredCourses.length === 0 ? (
            <div className="no-results">
              <h3>No courses found</h3>
              <p>Try adjusting your search criteria or clearing the filters.</p>
              <button onClick={clearFilters} className="btn">Clear All Filters</button>
            </div>
          ) : (
            <div className="courses-grid">
              {filteredCourses.map(course => (
                <div key={course.id} className="course-card card">
                  <div className="course-header">
                    <div className="course-title-section">
                      <h3 className="course-title">{course.title}</h3>
                      <span className="course-code">{course.code}</span>
                    </div>
                    <div className="course-level">
                      <span className={`level-badge ${course.level.toLowerCase()}`}>
                        {course.level}
                      </span>
                    </div>
                  </div>

                  <div className="course-content">
                    <p className="course-description">{course.description}</p>
                    
                    <div className="course-details">
                      <div className="detail-item">
                        <i className="fas fa-building"></i>
                        <span>{course.department}</span>
                      </div>
                      <div className="detail-item">
                        <i className="fas fa-clock"></i>
                        <span>{course.duration}</span>
                      </div>
                      <div className="detail-item">
                        <i className="fas fa-graduation-cap"></i>
                        <span>{course.credits} Credits</span>
                      </div>
                    </div>
                  </div>

                  <div className="course-footer">
                    <button className="btn btn-outline">Learn More</button>
                    <button className="btn btn-primary">Apply Now</button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="courses-cta section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Begin Your Academic Journey?</h2>
            <p>Join thousands of students who have chosen Alghazi University for their education.</p>
            <div className="cta-buttons">
              <a href="/admissions" className="btn btn-primary">Apply for Admission</a>
              <a href="/contact" className="btn btn-secondary">Get More Information</a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Courses;
