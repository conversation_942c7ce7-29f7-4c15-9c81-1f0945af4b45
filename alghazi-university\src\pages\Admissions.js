import React, { useState, useEffect } from 'react';
import { loadAdmissionInfo, saveToLocalStorage, validateEmail, validatePhone, validateRequired } from '../utils/dataManager';
import './Admissions.css';

const Admissions = () => {
  const [admissionInfo, setAdmissionInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: '',
    address: '',
    program: '',
    level: '',
    previousEducation: '',
    gpa: '',
    testScores: '',
    personalStatement: '',
    hasRecommendations: false,
    agreeToTerms: false
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const admissionData = await loadAdmissionInfo();
        setAdmissionInfo(admissionData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading admission info:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'nationality', 'address', 'program', 'level', 'previousEducation', 'personalStatement'];
    
    requiredFields.forEach(field => {
      if (!validateRequired(formData[field])) {
        newErrors[field] = 'This field is required';
      }
    });

    // Email validation
    if (formData.email && !validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // GPA validation
    if (formData.gpa && (isNaN(formData.gpa) || formData.gpa < 0 || formData.gpa > 4)) {
      newErrors.gpa = 'GPA must be a number between 0 and 4';
    }

    // Terms agreement validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    }

    // Personal statement length validation
    if (formData.personalStatement && formData.personalStatement.length < 100) {
      newErrors.personalStatement = 'Personal statement must be at least 100 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Save to localStorage
      const success = saveToLocalStorage('admissionApplications', formData);
      
      if (success) {
        setSubmitSuccess(true);
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          dateOfBirth: '',
          nationality: '',
          address: '',
          program: '',
          level: '',
          previousEducation: '',
          gpa: '',
          testScores: '',
          personalStatement: '',
          hasRecommendations: false,
          agreeToTerms: false
        });
      } else {
        throw new Error('Failed to save application');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setErrors({ submit: 'Failed to submit application. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading admission information...</p>
      </div>
    );
  }

  if (submitSuccess) {
    return (
      <div className="admissions">
        <section className="success-section">
          <div className="container">
            <div className="success-content">
              <div className="success-icon">
                <i className="fas fa-check-circle"></i>
              </div>
              <h1>Application Submitted Successfully!</h1>
              <p>Thank you for your interest in Alghazi University. We have received your application and will review it carefully.</p>
              <p>You will receive a confirmation email shortly with your application reference number.</p>
              <div className="success-actions">
                <button 
                  onClick={() => setSubmitSuccess(false)} 
                  className="btn btn-primary"
                >
                  Submit Another Application
                </button>
                <a href="/" className="btn btn-secondary">Return to Home</a>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="admissions">
      {/* Hero Section */}
      <section className="admissions-hero">
        <div className="container">
          <h1>Admissions</h1>
          <p>Begin your journey to academic excellence at Alghazi University</p>
        </div>
      </section>

      {/* Requirements Section */}
      <section className="requirements section">
        <div className="container">
          <h2 className="section-title">Admission Requirements</h2>
          <div className="requirements-grid">
            <div className="requirement-card card">
              <h3>Undergraduate Programs</h3>
              <ul>
                {admissionInfo.requirements?.undergraduate?.map((req, index) => (
                  <li key={index}>{req}</li>
                ))}
              </ul>
            </div>
            <div className="requirement-card card">
              <h3>Graduate Programs</h3>
              <ul>
                {admissionInfo.requirements?.graduate?.map((req, index) => (
                  <li key={index}>{req}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Application Form Section */}
      <section className="application-form section">
        <div className="container">
          <h2 className="section-title">Application Form</h2>
          <div className="form-container">
            <form onSubmit={handleSubmit} className="admission-form">
              {/* Personal Information */}
              <div className="form-section">
                <h3>Personal Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">First Name *</label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className={`form-input ${errors.firstName ? 'error' : ''}`}
                    />
                    {errors.firstName && <span className="form-error">{errors.firstName}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Last Name *</label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className={`form-input ${errors.lastName ? 'error' : ''}`}
                    />
                    {errors.lastName && <span className="form-error">{errors.lastName}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`form-input ${errors.email ? 'error' : ''}`}
                    />
                    {errors.email && <span className="form-error">{errors.email}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Phone Number *</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`form-input ${errors.phone ? 'error' : ''}`}
                    />
                    {errors.phone && <span className="form-error">{errors.phone}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Date of Birth *</label>
                    <input
                      type="date"
                      name="dateOfBirth"
                      value={formData.dateOfBirth}
                      onChange={handleInputChange}
                      className={`form-input ${errors.dateOfBirth ? 'error' : ''}`}
                    />
                    {errors.dateOfBirth && <span className="form-error">{errors.dateOfBirth}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Nationality *</label>
                    <input
                      type="text"
                      name="nationality"
                      value={formData.nationality}
                      onChange={handleInputChange}
                      className={`form-input ${errors.nationality ? 'error' : ''}`}
                    />
                    {errors.nationality && <span className="form-error">{errors.nationality}</span>}
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Address *</label>
                  <textarea
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className={`form-textarea ${errors.address ? 'error' : ''}`}
                    rows="3"
                  />
                  {errors.address && <span className="form-error">{errors.address}</span>}
                </div>
              </div>

              {/* Academic Information */}
              <div className="form-section">
                <h3>Academic Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Desired Program *</label>
                    <input
                      type="text"
                      name="program"
                      value={formData.program}
                      onChange={handleInputChange}
                      className={`form-input ${errors.program ? 'error' : ''}`}
                      placeholder="e.g., Computer Science, Business Administration"
                    />
                    {errors.program && <span className="form-error">{errors.program}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Level *</label>
                    <select
                      name="level"
                      value={formData.level}
                      onChange={handleInputChange}
                      className={`form-select ${errors.level ? 'error' : ''}`}
                    >
                      <option value="">Select Level</option>
                      <option value="undergraduate">Undergraduate</option>
                      <option value="graduate">Graduate</option>
                    </select>
                    {errors.level && <span className="form-error">{errors.level}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Previous Education *</label>
                    <input
                      type="text"
                      name="previousEducation"
                      value={formData.previousEducation}
                      onChange={handleInputChange}
                      className={`form-input ${errors.previousEducation ? 'error' : ''}`}
                      placeholder="e.g., High School Diploma, Bachelor's Degree"
                    />
                    {errors.previousEducation && <span className="form-error">{errors.previousEducation}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">GPA (if applicable)</label>
                    <input
                      type="number"
                      name="gpa"
                      value={formData.gpa}
                      onChange={handleInputChange}
                      className={`form-input ${errors.gpa ? 'error' : ''}`}
                      step="0.01"
                      min="0"
                      max="4"
                      placeholder="e.g., 3.75"
                    />
                    {errors.gpa && <span className="form-error">{errors.gpa}</span>}
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Test Scores (SAT, GRE, GMAT, etc.)</label>
                  <input
                    type="text"
                    name="testScores"
                    value={formData.testScores}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="e.g., SAT: 1450, GRE: 320"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Personal Statement *</label>
                  <textarea
                    name="personalStatement"
                    value={formData.personalStatement}
                    onChange={handleInputChange}
                    className={`form-textarea ${errors.personalStatement ? 'error' : ''}`}
                    rows="6"
                    placeholder="Tell us about yourself, your goals, and why you want to study at Alghazi University (minimum 100 characters)"
                  />
                  <div className="character-count">
                    {formData.personalStatement.length} characters
                  </div>
                  {errors.personalStatement && <span className="form-error">{errors.personalStatement}</span>}
                </div>

                <div className="form-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="hasRecommendations"
                      checked={formData.hasRecommendations}
                      onChange={handleInputChange}
                    />
                    I have letters of recommendation ready to submit
                  </label>
                </div>

                <div className="form-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="agreeToTerms"
                      checked={formData.agreeToTerms}
                      onChange={handleInputChange}
                    />
                    I agree to the terms and conditions and privacy policy *
                  </label>
                  {errors.agreeToTerms && <span className="form-error">{errors.agreeToTerms}</span>}
                </div>
              </div>

              {errors.submit && (
                <div className="form-error submit-error">{errors.submit}</div>
              )}

              <div className="form-actions">
                <button 
                  type="submit" 
                  className="btn btn-primary submit-btn"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Application'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Additional Info Section */}
      <section className="additional-info section">
        <div className="container">
          <div className="info-grid">
            <div className="info-card card">
              <h3>Application Deadlines</h3>
              <ul>
                <li><strong>Fall Semester:</strong> {admissionInfo.deadlines?.fall}</li>
                <li><strong>Spring Semester:</strong> {admissionInfo.deadlines?.spring}</li>
                <li><strong>Summer Semester:</strong> {admissionInfo.deadlines?.summer}</li>
              </ul>
            </div>
            <div className="info-card card">
              <h3>Tuition Fees</h3>
              <div className="tuition-info">
                <h4>Undergraduate</h4>
                <p>Domestic: {admissionInfo.tuition?.undergraduate?.domestic}</p>
                <p>International: {admissionInfo.tuition?.undergraduate?.international}</p>
                <h4>Graduate</h4>
                <p>Domestic: {admissionInfo.tuition?.graduate?.domestic}</p>
                <p>International: {admissionInfo.tuition?.graduate?.international}</p>
              </div>
            </div>
            <div className="info-card card">
              <h3>Scholarships Available</h3>
              <ul>
                {admissionInfo.scholarships?.map((scholarship, index) => (
                  <li key={index}>
                    <strong>{scholarship.name}:</strong> {scholarship.amount}
                    <br />
                    <small>{scholarship.criteria}</small>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Admissions;
