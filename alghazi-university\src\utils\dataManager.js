// Data management utilities for localStorage and JSON data

// Load JSON data
export const loadCourses = async () => {
  try {
    const response = await import('../data/courses.json');
    return response.default;
  } catch (error) {
    console.error('Error loading courses:', error);
    return [];
  }
};

export const loadUniversityInfo = async () => {
  try {
    const response = await import('../data/universityInfo.json');
    return response.default;
  } catch (error) {
    console.error('Error loading university info:', error);
    return {};
  }
};

export const loadAdmissionInfo = async () => {
  try {
    const response = await import('../data/admissionInfo.json');
    return response.default;
  } catch (error) {
    console.error('Error loading admission info:', error);
    return {};
  }
};

// LocalStorage utilities for form submissions
export const saveToLocalStorage = (key, data) => {
  try {
    const existingData = JSON.parse(localStorage.getItem(key) || '[]');
    const newData = [...existingData, { ...data, id: Date.now(), timestamp: new Date().toISOString() }];
    localStorage.setItem(key, JSON.stringify(newData));
    return true;
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    return false;
  }
};

export const getFromLocalStorage = (key) => {
  try {
    return JSON.parse(localStorage.getItem(key) || '[]');
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return [];
  }
};

export const clearLocalStorage = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
};

// Form validation utilities
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

export const validateRequired = (value) => {
  return value && value.toString().trim().length > 0;
};

// Course filtering utilities
export const filterCourses = (courses, filters) => {
  return courses.filter(course => {
    const matchesDepartment = !filters.department || course.department === filters.department;
    const matchesLevel = !filters.level || course.level === filters.level;
    const matchesSearch = !filters.search || 
      course.title.toLowerCase().includes(filters.search.toLowerCase()) ||
      course.code.toLowerCase().includes(filters.search.toLowerCase()) ||
      course.description.toLowerCase().includes(filters.search.toLowerCase());
    
    return matchesDepartment && matchesLevel && matchesSearch;
  });
};

export const getDepartments = (courses) => {
  const departments = [...new Set(courses.map(course => course.department))];
  return departments.sort();
};

export const getLevels = (courses) => {
  const levels = [...new Set(courses.map(course => course.level))];
  return levels.sort();
};
