{"ast": null, "code": "var _jsxFileName = \"D:\\\\vscode project do not open\\\\alghazi university\\\\alghazi-university\\\\src\\\\pages\\\\Contact.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { loadUniversityInfo, saveToLocalStorage, validateEmail, validatePhone, validateRequired } from '../utils/dataManager';\nimport './Contact.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  var _universityInfo$conta, _universityInfo$conta2, _universityInfo$conta3, _universityInfo$conta4, _universityInfo$conta5, _universityInfo$conta6, _universityInfo$conta7, _universityInfo$socia, _universityInfo$socia2, _universityInfo$socia3, _universityInfo$socia4, _universityInfo$conta8;\n  const [universityInfo, setUniversityInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    subject: '',\n    message: '',\n    inquiryType: 'general'\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const uniInfo = await loadUniversityInfo();\n        setUniversityInfo(uniInfo);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading university info:', error);\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    const requiredFields = ['name', 'email', 'subject', 'message'];\n    requiredFields.forEach(field => {\n      if (!validateRequired(formData[field])) {\n        newErrors[field] = 'This field is required';\n      }\n    });\n\n    // Email validation\n    if (formData.email && !validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation (optional but if provided, must be valid)\n    if (formData.phone && !validatePhone(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // Message length validation\n    if (formData.message && formData.message.length < 10) {\n      newErrors.message = 'Message must be at least 10 characters long';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Save to localStorage\n      const success = saveToLocalStorage('contactSubmissions', formData);\n      if (success) {\n        setSubmitSuccess(true);\n        setFormData({\n          name: '',\n          email: '',\n          phone: '',\n          subject: '',\n          message: '',\n          inquiryType: 'general'\n        });\n\n        // Hide success message after 5 seconds\n        setTimeout(() => {\n          setSubmitSuccess(false);\n        }, 5000);\n      } else {\n        throw new Error('Failed to save contact form');\n      }\n    } catch (error) {\n      console.error('Error submitting contact form:', error);\n      setErrors({\n        submit: 'Failed to send message. Please try again.'\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading contact information...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"contact\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Get in touch with Alghazi University - we're here to help\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-info-section section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-map-marker-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Visit Our Campus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta = universityInfo.contact) === null || _universityInfo$conta === void 0 ? void 0 : _universityInfo$conta.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#map\",\n              className: \"contact-link\",\n              children: \"View on Map\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Call Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta2 = universityInfo.contact) === null || _universityInfo$conta2 === void 0 ? void 0 : _universityInfo$conta2.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `tel:${(_universityInfo$conta3 = universityInfo.contact) === null || _universityInfo$conta3 === void 0 ? void 0 : _universityInfo$conta3.phone}`,\n              className: \"contact-link\",\n              children: \"Call Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-envelope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Email Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta4 = universityInfo.contact) === null || _universityInfo$conta4 === void 0 ? void 0 : _universityInfo$conta4.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `mailto:${(_universityInfo$conta5 = universityInfo.contact) === null || _universityInfo$conta5 === void 0 ? void 0 : _universityInfo$conta5.email}`,\n              className: \"contact-link\",\n              children: \"Send Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info-card card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-globe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Website\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: (_universityInfo$conta6 = universityInfo.contact) === null || _universityInfo$conta6 === void 0 ? void 0 : _universityInfo$conta6.website\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `https://${(_universityInfo$conta7 = universityInfo.contact) === null || _universityInfo$conta7 === void 0 ? void 0 : _universityInfo$conta7.website}`,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"contact-link\",\n              children: \"Visit Website\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-form-section section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-form-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Send us a Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Have a question or need more information? Fill out the form below and we'll get back to you as soon as possible.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), submitSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-message\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Thank you! Your message has been sent successfully. We'll get back to you soon.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              className: \"contact-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Full Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.name ? 'error' : ''}`,\n                    placeholder: \"Enter your full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Email Address *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.email ? 'error' : ''}`,\n                    placeholder: \"Enter your email address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phone\",\n                    value: formData.phone,\n                    onChange: handleInputChange,\n                    className: `form-input ${errors.phone ? 'error' : ''}`,\n                    placeholder: \"Enter your phone number (optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"form-error\",\n                    children: errors.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Inquiry Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"inquiryType\",\n                    value: formData.inquiryType,\n                    onChange: handleInputChange,\n                    className: \"form-select\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"general\",\n                      children: \"General Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"admissions\",\n                      children: \"Admissions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"academics\",\n                      children: \"Academic Programs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"financial\",\n                      children: \"Financial Aid\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"campus\",\n                      children: \"Campus Life\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"research\",\n                      children: \"Research\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"other\",\n                      children: \"Other\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Subject *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"subject\",\n                  value: formData.subject,\n                  onChange: handleInputChange,\n                  className: `form-input ${errors.subject ? 'error' : ''}`,\n                  placeholder: \"Enter the subject of your inquiry\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), errors.subject && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-error\",\n                  children: errors.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 38\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Message *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"message\",\n                  value: formData.message,\n                  onChange: handleInputChange,\n                  className: `form-textarea ${errors.message ? 'error' : ''}`,\n                  rows: \"6\",\n                  placeholder: \"Enter your message or question here...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"character-count\",\n                  children: [formData.message.length, \" characters\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), errors.message && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-error\",\n                  children: errors.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 38\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-error submit-error\",\n                children: errors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"btn btn-primary submit-btn\",\n                  disabled: isSubmitting,\n                  children: isSubmitting ? 'Sending...' : 'Send Message'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-sidebar\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-card card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Office Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"office-hours\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hours-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"day\",\n                    children: \"Monday - Friday\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"time\",\n                    children: \"8:00 AM - 6:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hours-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"day\",\n                    children: \"Saturday\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"time\",\n                    children: \"9:00 AM - 4:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hours-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"day\",\n                    children: \"Sunday\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"time\",\n                    children: \"Closed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-card card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Quick Links\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"quick-links\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"/admissions\",\n                    children: \"Admissions Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"/courses\",\n                    children: \"Academic Programs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#financial-aid\",\n                    children: \"Financial Aid\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#campus-tours\",\n                    children: \"Campus Tours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#student-services\",\n                    children: \"Student Services\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-card card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Follow Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"social-links\",\n                children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                  href: (_universityInfo$socia = universityInfo.socialMedia) === null || _universityInfo$socia === void 0 ? void 0 : _universityInfo$socia.facebook,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"social-link facebook\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fab fa-facebook-f\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Facebook\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: (_universityInfo$socia2 = universityInfo.socialMedia) === null || _universityInfo$socia2 === void 0 ? void 0 : _universityInfo$socia2.twitter,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"social-link twitter\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fab fa-twitter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Twitter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: (_universityInfo$socia3 = universityInfo.socialMedia) === null || _universityInfo$socia3 === void 0 ? void 0 : _universityInfo$socia3.instagram,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"social-link instagram\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fab fa-instagram\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Instagram\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: (_universityInfo$socia4 = universityInfo.socialMedia) === null || _universityInfo$socia4 === void 0 ? void 0 : _universityInfo$socia4.linkedin,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"social-link linkedin\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fab fa-linkedin-in\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"LinkedIn\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"map-section\",\n      id: \"map\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Find Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"map-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-placeholder\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"map-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-map-marker-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Alghazi University Campus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: (_universityInfo$conta8 = universityInfo.contact) === null || _universityInfo$conta8 === void 0 ? void 0 : _universityInfo$conta8.address\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Interactive map would be embedded here using Google Maps or similar service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Contact, \"9PkY/PffZDQbnmH/c4Hk+CjW8EQ=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "loadUniversityInfo", "saveToLocalStorage", "validateEmail", "validatePhone", "validateRequired", "jsxDEV", "_jsxDEV", "Contact", "_s", "_universityInfo$conta", "_universityInfo$conta2", "_universityInfo$conta3", "_universityInfo$conta4", "_universityInfo$conta5", "_universityInfo$conta6", "_universityInfo$conta7", "_universityInfo$socia", "_universityInfo$socia2", "_universityInfo$socia3", "_universityInfo$socia4", "_universityInfo$conta8", "universityInfo", "setUniversityInfo", "loading", "setLoading", "formData", "setFormData", "name", "email", "phone", "subject", "message", "inquiryType", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "submitSuccess", "setSubmitSuccess", "fetchData", "uniInfo", "error", "console", "handleInputChange", "e", "value", "target", "prev", "validateForm", "newErrors", "requiredFields", "for<PERSON>ach", "field", "length", "Object", "keys", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "success", "Error", "submit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "contact", "address", "href", "website", "rel", "onSubmit", "type", "onChange", "placeholder", "rows", "disabled", "socialMedia", "facebook", "twitter", "instagram", "linkedin", "id", "_c", "$RefreshReg$"], "sources": ["D:/vscode project do not open/alghazi university/alghazi-university/src/pages/Contact.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { loadUniversityInfo, saveToLocalStorage, validateEmail, validatePhone, validateRequired } from '../utils/dataManager';\nimport './Contact.css';\n\nconst Contact = () => {\n  const [universityInfo, setUniversityInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    subject: '',\n    message: '',\n    inquiryType: 'general'\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const uniInfo = await loadUniversityInfo();\n        setUniversityInfo(uniInfo);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading university info:', error);\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    const requiredFields = ['name', 'email', 'subject', 'message'];\n    \n    requiredFields.forEach(field => {\n      if (!validateRequired(formData[field])) {\n        newErrors[field] = 'This field is required';\n      }\n    });\n\n    // Email validation\n    if (formData.email && !validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation (optional but if provided, must be valid)\n    if (formData.phone && !validatePhone(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // Message length validation\n    if (formData.message && formData.message.length < 10) {\n      newErrors.message = 'Message must be at least 10 characters long';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Save to localStorage\n      const success = saveToLocalStorage('contactSubmissions', formData);\n      \n      if (success) {\n        setSubmitSuccess(true);\n        setFormData({\n          name: '',\n          email: '',\n          phone: '',\n          subject: '',\n          message: '',\n          inquiryType: 'general'\n        });\n        \n        // Hide success message after 5 seconds\n        setTimeout(() => {\n          setSubmitSuccess(false);\n        }, 5000);\n      } else {\n        throw new Error('Failed to save contact form');\n      }\n    } catch (error) {\n      console.error('Error submitting contact form:', error);\n      setErrors({ submit: 'Failed to send message. Please try again.' });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading contact information...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"contact\">\n      {/* Hero Section */}\n      <section className=\"contact-hero\">\n        <div className=\"container\">\n          <h1>Contact Us</h1>\n          <p>Get in touch with Alghazi University - we're here to help</p>\n        </div>\n      </section>\n\n      {/* Contact Info Section */}\n      <section className=\"contact-info-section section\">\n        <div className=\"container\">\n          <div className=\"contact-info-grid\">\n            <div className=\"contact-info-card card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-map-marker-alt\"></i>\n              </div>\n              <h3>Visit Our Campus</h3>\n              <p>{universityInfo.contact?.address}</p>\n              <a href=\"#map\" className=\"contact-link\">View on Map</a>\n            </div>\n\n            <div className=\"contact-info-card card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-phone\"></i>\n              </div>\n              <h3>Call Us</h3>\n              <p>{universityInfo.contact?.phone}</p>\n              <a href={`tel:${universityInfo.contact?.phone}`} className=\"contact-link\">Call Now</a>\n            </div>\n\n            <div className=\"contact-info-card card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-envelope\"></i>\n              </div>\n              <h3>Email Us</h3>\n              <p>{universityInfo.contact?.email}</p>\n              <a href={`mailto:${universityInfo.contact?.email}`} className=\"contact-link\">Send Email</a>\n            </div>\n\n            <div className=\"contact-info-card card\">\n              <div className=\"contact-icon\">\n                <i className=\"fas fa-globe\"></i>\n              </div>\n              <h3>Website</h3>\n              <p>{universityInfo.contact?.website}</p>\n              <a href={`https://${universityInfo.contact?.website}`} target=\"_blank\" rel=\"noopener noreferrer\" className=\"contact-link\">Visit Website</a>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form Section */}\n      <section className=\"contact-form-section section\">\n        <div className=\"container\">\n          <div className=\"contact-content\">\n            <div className=\"contact-form-container\">\n              <h2>Send us a Message</h2>\n              <p>Have a question or need more information? Fill out the form below and we'll get back to you as soon as possible.</p>\n              \n              {submitSuccess && (\n                <div className=\"success-message\">\n                  <i className=\"fas fa-check-circle\"></i>\n                  <span>Thank you! Your message has been sent successfully. We'll get back to you soon.</span>\n                </div>\n              )}\n\n              <form onSubmit={handleSubmit} className=\"contact-form\">\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Full Name *</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.name ? 'error' : ''}`}\n                      placeholder=\"Enter your full name\"\n                    />\n                    {errors.name && <span className=\"form-error\">{errors.name}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Email Address *</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.email ? 'error' : ''}`}\n                      placeholder=\"Enter your email address\"\n                    />\n                    {errors.email && <span className=\"form-error\">{errors.email}</span>}\n                  </div>\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Phone Number</label>\n                    <input\n                      type=\"tel\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleInputChange}\n                      className={`form-input ${errors.phone ? 'error' : ''}`}\n                      placeholder=\"Enter your phone number (optional)\"\n                    />\n                    {errors.phone && <span className=\"form-error\">{errors.phone}</span>}\n                  </div>\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">Inquiry Type</label>\n                    <select\n                      name=\"inquiryType\"\n                      value={formData.inquiryType}\n                      onChange={handleInputChange}\n                      className=\"form-select\"\n                    >\n                      <option value=\"general\">General Information</option>\n                      <option value=\"admissions\">Admissions</option>\n                      <option value=\"academics\">Academic Programs</option>\n                      <option value=\"financial\">Financial Aid</option>\n                      <option value=\"campus\">Campus Life</option>\n                      <option value=\"research\">Research</option>\n                      <option value=\"other\">Other</option>\n                    </select>\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Subject *</label>\n                  <input\n                    type=\"text\"\n                    name=\"subject\"\n                    value={formData.subject}\n                    onChange={handleInputChange}\n                    className={`form-input ${errors.subject ? 'error' : ''}`}\n                    placeholder=\"Enter the subject of your inquiry\"\n                  />\n                  {errors.subject && <span className=\"form-error\">{errors.subject}</span>}\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Message *</label>\n                  <textarea\n                    name=\"message\"\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    className={`form-textarea ${errors.message ? 'error' : ''}`}\n                    rows=\"6\"\n                    placeholder=\"Enter your message or question here...\"\n                  />\n                  <div className=\"character-count\">\n                    {formData.message.length} characters\n                  </div>\n                  {errors.message && <span className=\"form-error\">{errors.message}</span>}\n                </div>\n\n                {errors.submit && (\n                  <div className=\"form-error submit-error\">{errors.submit}</div>\n                )}\n\n                <div className=\"form-actions\">\n                  <button \n                    type=\"submit\" \n                    className=\"btn btn-primary submit-btn\"\n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? 'Sending...' : 'Send Message'}\n                  </button>\n                </div>\n              </form>\n            </div>\n\n            <div className=\"contact-sidebar\">\n              <div className=\"sidebar-card card\">\n                <h3>Office Hours</h3>\n                <div className=\"office-hours\">\n                  <div className=\"hours-item\">\n                    <span className=\"day\">Monday - Friday</span>\n                    <span className=\"time\">8:00 AM - 6:00 PM</span>\n                  </div>\n                  <div className=\"hours-item\">\n                    <span className=\"day\">Saturday</span>\n                    <span className=\"time\">9:00 AM - 4:00 PM</span>\n                  </div>\n                  <div className=\"hours-item\">\n                    <span className=\"day\">Sunday</span>\n                    <span className=\"time\">Closed</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"sidebar-card card\">\n                <h3>Quick Links</h3>\n                <ul className=\"quick-links\">\n                  <li><a href=\"/admissions\">Admissions Information</a></li>\n                  <li><a href=\"/courses\">Academic Programs</a></li>\n                  <li><a href=\"#financial-aid\">Financial Aid</a></li>\n                  <li><a href=\"#campus-tours\">Campus Tours</a></li>\n                  <li><a href=\"#student-services\">Student Services</a></li>\n                </ul>\n              </div>\n\n              <div className=\"sidebar-card card\">\n                <h3>Follow Us</h3>\n                <div className=\"social-links\">\n                  <a href={universityInfo.socialMedia?.facebook} target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-link facebook\">\n                    <i className=\"fab fa-facebook-f\"></i>\n                    <span>Facebook</span>\n                  </a>\n                  <a href={universityInfo.socialMedia?.twitter} target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-link twitter\">\n                    <i className=\"fab fa-twitter\"></i>\n                    <span>Twitter</span>\n                  </a>\n                  <a href={universityInfo.socialMedia?.instagram} target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-link instagram\">\n                    <i className=\"fab fa-instagram\"></i>\n                    <span>Instagram</span>\n                  </a>\n                  <a href={universityInfo.socialMedia?.linkedin} target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-link linkedin\">\n                    <i className=\"fab fa-linkedin-in\"></i>\n                    <span>LinkedIn</span>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Map Section */}\n      <section className=\"map-section\" id=\"map\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Find Us</h2>\n          <div className=\"map-container\">\n            <div className=\"map-placeholder\">\n              <div className=\"map-content\">\n                <i className=\"fas fa-map-marker-alt\"></i>\n                <h3>Alghazi University Campus</h3>\n                <p>{universityInfo.contact?.address}</p>\n                <p>Interactive map would be embedded here using Google Maps or similar service</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC7H,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC;IACvC6B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,MAAMwC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,OAAO,GAAG,MAAMxC,kBAAkB,CAAC,CAAC;QAC1CsB,iBAAiB,CAACkB,OAAO,CAAC;QAC1BhB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDjB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDe,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEjB,IAAI;MAAEkB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCpB,WAAW,CAACqB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACpB,IAAI,GAAGkB;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIZ,MAAM,CAACN,IAAI,CAAC,EAAE;MAChBO,SAAS,CAACa,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACpB,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;IAE9DA,cAAc,CAACC,OAAO,CAACC,KAAK,IAAI;MAC9B,IAAI,CAAChD,gBAAgB,CAACqB,QAAQ,CAAC2B,KAAK,CAAC,CAAC,EAAE;QACtCH,SAAS,CAACG,KAAK,CAAC,GAAG,wBAAwB;MAC7C;IACF,CAAC,CAAC;;IAEF;IACA,IAAI3B,QAAQ,CAACG,KAAK,IAAI,CAAC1B,aAAa,CAACuB,QAAQ,CAACG,KAAK,CAAC,EAAE;MACpDqB,SAAS,CAACrB,KAAK,GAAG,oCAAoC;IACxD;;IAEA;IACA,IAAIH,QAAQ,CAACI,KAAK,IAAI,CAAC1B,aAAa,CAACsB,QAAQ,CAACI,KAAK,CAAC,EAAE;MACpDoB,SAAS,CAACpB,KAAK,GAAG,mCAAmC;IACvD;;IAEA;IACA,IAAIJ,QAAQ,CAACM,OAAO,IAAIN,QAAQ,CAACM,OAAO,CAACsB,MAAM,GAAG,EAAE,EAAE;MACpDJ,SAAS,CAAClB,OAAO,GAAG,6CAA6C;IACnE;IAEAG,SAAS,CAACe,SAAS,CAAC;IACpB,OAAOK,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACT,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAZ,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM,IAAIsB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,OAAO,GAAG5D,kBAAkB,CAAC,oBAAoB,EAAEwB,QAAQ,CAAC;MAElE,IAAIoC,OAAO,EAAE;QACXvB,gBAAgB,CAAC,IAAI,CAAC;QACtBZ,WAAW,CAAC;UACVC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE,EAAE;UACXC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA4B,UAAU,CAAC,MAAM;UACftB,gBAAgB,CAAC,KAAK,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIwB,KAAK,CAAC,6BAA6B,CAAC;MAChD;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDP,SAAS,CAAC;QAAE6B,MAAM,EAAE;MAA4C,CAAC,CAAC;IACpE,CAAC,SAAS;MACR3B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,IAAIb,OAAO,EAAE;IACX,oBACEjB,OAAA;MAAK0D,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtB3D,OAAA;QAAK0D,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC/D,OAAA;QAAA2D,QAAA,EAAG;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;EAEA,oBACE/D,OAAA;IAAK0D,SAAS,EAAC,SAAS;IAAAC,QAAA,gBAEtB3D,OAAA;MAAS0D,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC/B3D,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3D,OAAA;UAAA2D,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnB/D,OAAA;UAAA2D,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/D,OAAA;MAAS0D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC/C3D,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3D,OAAA;YAAK0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3D,OAAA;gBAAG0D,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN/D,OAAA;cAAA2D,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB/D,OAAA;cAAA2D,QAAA,GAAAxD,qBAAA,GAAIY,cAAc,CAACiD,OAAO,cAAA7D,qBAAA,uBAAtBA,qBAAA,CAAwB8D;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxC/D,OAAA;cAAGkE,IAAI,EAAC,MAAM;cAACR,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAEN/D,OAAA;YAAK0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3D,OAAA;gBAAG0D,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACN/D,OAAA;cAAA2D,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB/D,OAAA;cAAA2D,QAAA,GAAAvD,sBAAA,GAAIW,cAAc,CAACiD,OAAO,cAAA5D,sBAAA,uBAAtBA,sBAAA,CAAwBmB;YAAK;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtC/D,OAAA;cAAGkE,IAAI,EAAE,QAAA7D,sBAAA,GAAOU,cAAc,CAACiD,OAAO,cAAA3D,sBAAA,uBAAtBA,sBAAA,CAAwBkB,KAAK,EAAG;cAACmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEN/D,OAAA;YAAK0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3D,OAAA;gBAAG0D,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN/D,OAAA;cAAA2D,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjB/D,OAAA;cAAA2D,QAAA,GAAArD,sBAAA,GAAIS,cAAc,CAACiD,OAAO,cAAA1D,sBAAA,uBAAtBA,sBAAA,CAAwBgB;YAAK;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtC/D,OAAA;cAAGkE,IAAI,EAAE,WAAA3D,sBAAA,GAAUQ,cAAc,CAACiD,OAAO,cAAAzD,sBAAA,uBAAtBA,sBAAA,CAAwBe,KAAK,EAAG;cAACoC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eAEN/D,OAAA;YAAK0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3D,OAAA;gBAAG0D,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACN/D,OAAA;cAAA2D,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB/D,OAAA;cAAA2D,QAAA,GAAAnD,sBAAA,GAAIO,cAAc,CAACiD,OAAO,cAAAxD,sBAAA,uBAAtBA,sBAAA,CAAwB2D;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxC/D,OAAA;cAAGkE,IAAI,EAAE,YAAAzD,sBAAA,GAAWM,cAAc,CAACiD,OAAO,cAAAvD,sBAAA,uBAAtBA,sBAAA,CAAwB0D,OAAO,EAAG;cAAC3B,MAAM,EAAC,QAAQ;cAAC4B,GAAG,EAAC,qBAAqB;cAACV,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/D,OAAA;MAAS0D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC/C3D,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3D,OAAA;UAAK0D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B3D,OAAA;YAAK0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3D,OAAA;cAAA2D,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B/D,OAAA;cAAA2D,QAAA,EAAG;YAAgH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAEtHhC,aAAa,iBACZ/B,OAAA;cAAK0D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3D,OAAA;gBAAG0D,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC/D,OAAA;gBAAA2D,QAAA,EAAM;cAA+E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CACN,eAED/D,OAAA;cAAMqE,QAAQ,EAAEnB,YAAa;cAACQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACpD3D,OAAA;gBAAK0D,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB3D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAO0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjD/D,OAAA;oBACEsE,IAAI,EAAC,MAAM;oBACXjD,IAAI,EAAC,MAAM;oBACXkB,KAAK,EAAEpB,QAAQ,CAACE,IAAK;oBACrBkD,QAAQ,EAAElC,iBAAkB;oBAC5BqB,SAAS,EAAE,cAAc/B,MAAM,CAACN,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;oBACtDmD,WAAW,EAAC;kBAAsB;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACDpC,MAAM,CAACN,IAAI,iBAAIrB,OAAA;oBAAM0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEhC,MAAM,CAACN;kBAAI;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAO0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrD/D,OAAA;oBACEsE,IAAI,EAAC,OAAO;oBACZjD,IAAI,EAAC,OAAO;oBACZkB,KAAK,EAAEpB,QAAQ,CAACG,KAAM;oBACtBiD,QAAQ,EAAElC,iBAAkB;oBAC5BqB,SAAS,EAAE,cAAc/B,MAAM,CAACL,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;oBACvDkD,WAAW,EAAC;kBAA0B;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,EACDpC,MAAM,CAACL,KAAK,iBAAItB,OAAA;oBAAM0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEhC,MAAM,CAACL;kBAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB3D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAO0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClD/D,OAAA;oBACEsE,IAAI,EAAC,KAAK;oBACVjD,IAAI,EAAC,OAAO;oBACZkB,KAAK,EAAEpB,QAAQ,CAACI,KAAM;oBACtBgD,QAAQ,EAAElC,iBAAkB;oBAC5BqB,SAAS,EAAE,cAAc/B,MAAM,CAACJ,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;oBACvDiD,WAAW,EAAC;kBAAoC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,EACDpC,MAAM,CAACJ,KAAK,iBAAIvB,OAAA;oBAAM0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEhC,MAAM,CAACJ;kBAAK;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAO0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClD/D,OAAA;oBACEqB,IAAI,EAAC,aAAa;oBAClBkB,KAAK,EAAEpB,QAAQ,CAACO,WAAY;oBAC5B6C,QAAQ,EAAElC,iBAAkB;oBAC5BqB,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAEvB3D,OAAA;sBAAQuC,KAAK,EAAC,SAAS;sBAAAoB,QAAA,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpD/D,OAAA;sBAAQuC,KAAK,EAAC,YAAY;sBAAAoB,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9C/D,OAAA;sBAAQuC,KAAK,EAAC,WAAW;sBAAAoB,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpD/D,OAAA;sBAAQuC,KAAK,EAAC,WAAW;sBAAAoB,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChD/D,OAAA;sBAAQuC,KAAK,EAAC,QAAQ;sBAAAoB,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3C/D,OAAA;sBAAQuC,KAAK,EAAC,UAAU;sBAAAoB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1C/D,OAAA;sBAAQuC,KAAK,EAAC,OAAO;sBAAAoB,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAO0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/C/D,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXjD,IAAI,EAAC,SAAS;kBACdkB,KAAK,EAAEpB,QAAQ,CAACK,OAAQ;kBACxB+C,QAAQ,EAAElC,iBAAkB;kBAC5BqB,SAAS,EAAE,cAAc/B,MAAM,CAACH,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;kBACzDgD,WAAW,EAAC;gBAAmC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,EACDpC,MAAM,CAACH,OAAO,iBAAIxB,OAAA;kBAAM0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEhC,MAAM,CAACH;gBAAO;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAO0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/C/D,OAAA;kBACEqB,IAAI,EAAC,SAAS;kBACdkB,KAAK,EAAEpB,QAAQ,CAACM,OAAQ;kBACxB8C,QAAQ,EAAElC,iBAAkB;kBAC5BqB,SAAS,EAAE,iBAAiB/B,MAAM,CAACF,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;kBAC5DgD,IAAI,EAAC,GAAG;kBACRD,WAAW,EAAC;gBAAwC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACF/D,OAAA;kBAAK0D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAC7BxC,QAAQ,CAACM,OAAO,CAACsB,MAAM,EAAC,aAC3B;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACLpC,MAAM,CAACF,OAAO,iBAAIzB,OAAA;kBAAM0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEhC,MAAM,CAACF;gBAAO;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,EAELpC,MAAM,CAAC8B,MAAM,iBACZzD,OAAA;gBAAK0D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAEhC,MAAM,CAAC8B;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC9D,eAED/D,OAAA;gBAAK0D,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B3D,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACbZ,SAAS,EAAC,4BAA4B;kBACtCgB,QAAQ,EAAE7C,YAAa;kBAAA8B,QAAA,EAEtB9B,YAAY,GAAG,YAAY,GAAG;gBAAc;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN/D,OAAA;YAAK0D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B3D,OAAA;cAAK0D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3D,OAAA;gBAAA2D,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB/D,OAAA;gBAAK0D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAM0D,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5C/D,OAAA;oBAAM0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAM0D,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrC/D,OAAA;oBAAM0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3D,OAAA;oBAAM0D,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC/D,OAAA;oBAAM0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/D,OAAA;cAAK0D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3D,OAAA;gBAAA2D,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB/D,OAAA;gBAAI0D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,eAAI3D,OAAA;oBAAGkE,IAAI,EAAC,aAAa;oBAAAP,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzD/D,OAAA;kBAAA2D,QAAA,eAAI3D,OAAA;oBAAGkE,IAAI,EAAC,UAAU;oBAAAP,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD/D,OAAA;kBAAA2D,QAAA,eAAI3D,OAAA;oBAAGkE,IAAI,EAAC,gBAAgB;oBAAAP,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnD/D,OAAA;kBAAA2D,QAAA,eAAI3D,OAAA;oBAAGkE,IAAI,EAAC,eAAe;oBAAAP,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD/D,OAAA;kBAAA2D,QAAA,eAAI3D,OAAA;oBAAGkE,IAAI,EAAC,mBAAmB;oBAAAP,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEN/D,OAAA;cAAK0D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3D,OAAA;gBAAA2D,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB/D,OAAA;gBAAK0D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3D,OAAA;kBAAGkE,IAAI,GAAAxD,qBAAA,GAAEK,cAAc,CAAC4D,WAAW,cAAAjE,qBAAA,uBAA1BA,qBAAA,CAA4BkE,QAAS;kBAACpC,MAAM,EAAC,QAAQ;kBAAC4B,GAAG,EAAC,qBAAqB;kBAACV,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACvH3D,OAAA;oBAAG0D,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrC/D,OAAA;oBAAA2D,QAAA,EAAM;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACJ/D,OAAA;kBAAGkE,IAAI,GAAAvD,sBAAA,GAAEI,cAAc,CAAC4D,WAAW,cAAAhE,sBAAA,uBAA1BA,sBAAA,CAA4BkE,OAAQ;kBAACrC,MAAM,EAAC,QAAQ;kBAAC4B,GAAG,EAAC,qBAAqB;kBAACV,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACrH3D,OAAA;oBAAG0D,SAAS,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClC/D,OAAA;oBAAA2D,QAAA,EAAM;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACJ/D,OAAA;kBAAGkE,IAAI,GAAAtD,sBAAA,GAAEG,cAAc,CAAC4D,WAAW,cAAA/D,sBAAA,uBAA1BA,sBAAA,CAA4BkE,SAAU;kBAACtC,MAAM,EAAC,QAAQ;kBAAC4B,GAAG,EAAC,qBAAqB;kBAACV,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACzH3D,OAAA;oBAAG0D,SAAS,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpC/D,OAAA;oBAAA2D,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACJ/D,OAAA;kBAAGkE,IAAI,GAAArD,sBAAA,GAAEE,cAAc,CAAC4D,WAAW,cAAA9D,sBAAA,uBAA1BA,sBAAA,CAA4BkE,QAAS;kBAACvC,MAAM,EAAC,QAAQ;kBAAC4B,GAAG,EAAC,qBAAqB;kBAACV,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACvH3D,OAAA;oBAAG0D,SAAS,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtC/D,OAAA;oBAAA2D,QAAA,EAAM;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/D,OAAA;MAAS0D,SAAS,EAAC,aAAa;MAACsB,EAAE,EAAC,KAAK;MAAArB,QAAA,eACvC3D,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3D,OAAA;UAAI0D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C/D,OAAA;UAAK0D,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B3D,OAAA;YAAK0D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B3D,OAAA;cAAK0D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3D,OAAA;gBAAG0D,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzC/D,OAAA;gBAAA2D,QAAA,EAAI;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClC/D,OAAA;gBAAA2D,QAAA,GAAA7C,sBAAA,GAAIC,cAAc,CAACiD,OAAO,cAAAlD,sBAAA,uBAAtBA,sBAAA,CAAwBmD;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxC/D,OAAA;gBAAA2D,QAAA,EAAG;cAA2E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAvXID,OAAO;AAAAgF,EAAA,GAAPhF,OAAO;AAyXb,eAAeA,OAAO;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}