import React, { useState, useEffect } from 'react';
import { getFromLocalStorage, clearLocalStorage } from '../utils/dataManager';
import './Admin.css';

const Admin = () => {
  const [activeTab, setActiveTab] = useState('applications');
  const [applications, setApplications] = useState([]);
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = () => {
      try {
        const admissionApps = getFromLocalStorage('admissionApplications');
        const contactSubs = getFromLocalStorage('contactSubmissions');
        
        setApplications(admissionApps);
        setContacts(contactSubs);
        setLoading(false);
      } catch (error) {
        console.error('Error loading admin data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleClearData = (dataType) => {
    if (window.confirm(`Are you sure you want to clear all ${dataType}? This action cannot be undone.`)) {
      const key = dataType === 'applications' ? 'admissionApplications' : 'contactSubmissions';
      clearLocalStorage(key);
      
      if (dataType === 'applications') {
        setApplications([]);
      } else {
        setContacts([]);
      }
      
      alert(`All ${dataType} have been cleared.`);
    }
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading admin data...</p>
      </div>
    );
  }

  return (
    <div className="admin">
      {/* Header */}
      <section className="admin-header">
        <div className="container">
          <h1>Admin Panel</h1>
          <p>Manage university data and view submissions</p>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="admin-nav">
        <div className="container">
          <div className="nav-tabs">
            <button 
              className={`tab-btn ${activeTab === 'applications' ? 'active' : ''}`}
              onClick={() => setActiveTab('applications')}
            >
              <i className="fas fa-graduation-cap"></i>
              Admission Applications ({applications.length})
            </button>
            <button 
              className={`tab-btn ${activeTab === 'contacts' ? 'active' : ''}`}
              onClick={() => setActiveTab('contacts')}
            >
              <i className="fas fa-envelope"></i>
              Contact Submissions ({contacts.length})
            </button>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="admin-content section">
        <div className="container">
          {activeTab === 'applications' && (
            <div className="data-section">
              <div className="section-header">
                <h2>Admission Applications</h2>
                <button 
                  className="btn btn-danger"
                  onClick={() => handleClearData('applications')}
                  disabled={applications.length === 0}
                >
                  <i className="fas fa-trash"></i>
                  Clear All Applications
                </button>
              </div>

              {applications.length === 0 ? (
                <div className="no-data">
                  <i className="fas fa-inbox"></i>
                  <h3>No Applications Yet</h3>
                  <p>Admission applications will appear here when students submit them.</p>
                </div>
              ) : (
                <div className="data-grid">
                  {applications.map((app, index) => (
                    <div key={app.id || index} className="data-card card">
                      <div className="card-header">
                        <h3>{app.firstName} {app.lastName}</h3>
                        <span className="timestamp">{formatDate(app.timestamp)}</span>
                      </div>
                      <div className="card-content">
                        <div className="info-row">
                          <span className="label">Email:</span>
                          <span className="value">{app.email}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Phone:</span>
                          <span className="value">{app.phone}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Program:</span>
                          <span className="value">{app.program}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Level:</span>
                          <span className="value badge">{app.level}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Nationality:</span>
                          <span className="value">{app.nationality}</span>
                        </div>
                        {app.gpa && (
                          <div className="info-row">
                            <span className="label">GPA:</span>
                            <span className="value">{app.gpa}</span>
                          </div>
                        )}
                        <div className="info-row full-width">
                          <span className="label">Personal Statement:</span>
                          <p className="statement">{app.personalStatement}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'contacts' && (
            <div className="data-section">
              <div className="section-header">
                <h2>Contact Submissions</h2>
                <button 
                  className="btn btn-danger"
                  onClick={() => handleClearData('contacts')}
                  disabled={contacts.length === 0}
                >
                  <i className="fas fa-trash"></i>
                  Clear All Contacts
                </button>
              </div>

              {contacts.length === 0 ? (
                <div className="no-data">
                  <i className="fas fa-inbox"></i>
                  <h3>No Contact Submissions Yet</h3>
                  <p>Contact form submissions will appear here when visitors send messages.</p>
                </div>
              ) : (
                <div className="data-grid">
                  {contacts.map((contact, index) => (
                    <div key={contact.id || index} className="data-card card">
                      <div className="card-header">
                        <h3>{contact.name}</h3>
                        <span className="timestamp">{formatDate(contact.timestamp)}</span>
                      </div>
                      <div className="card-content">
                        <div className="info-row">
                          <span className="label">Email:</span>
                          <span className="value">{contact.email}</span>
                        </div>
                        {contact.phone && (
                          <div className="info-row">
                            <span className="label">Phone:</span>
                            <span className="value">{contact.phone}</span>
                          </div>
                        )}
                        <div className="info-row">
                          <span className="label">Inquiry Type:</span>
                          <span className="value badge">{contact.inquiryType}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Subject:</span>
                          <span className="value">{contact.subject}</span>
                        </div>
                        <div className="info-row full-width">
                          <span className="label">Message:</span>
                          <p className="message">{contact.message}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Statistics */}
      <section className="admin-stats section">
        <div className="container">
          <h2 className="section-title">Statistics</h2>
          <div className="stats-grid">
            <div className="stat-card card">
              <div className="stat-icon">
                <i className="fas fa-graduation-cap"></i>
              </div>
              <div className="stat-info">
                <h3>{applications.length}</h3>
                <p>Total Applications</p>
              </div>
            </div>
            <div className="stat-card card">
              <div className="stat-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <div className="stat-info">
                <h3>{contacts.length}</h3>
                <p>Contact Messages</p>
              </div>
            </div>
            <div className="stat-card card">
              <div className="stat-icon">
                <i className="fas fa-users"></i>
              </div>
              <div className="stat-info">
                <h3>{applications.length + contacts.length}</h3>
                <p>Total Interactions</p>
              </div>
            </div>
            <div className="stat-card card">
              <div className="stat-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <div className="stat-info">
                <h3>{applications.filter(app => app.level === 'undergraduate').length}</h3>
                <p>Undergraduate Apps</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Admin;
