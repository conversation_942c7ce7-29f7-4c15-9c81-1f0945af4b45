import React, { useState, useEffect } from 'react';
import { loadUniversityInfo } from '../utils/dataManager';
import './About.css';

const About = () => {
  const [universityInfo, setUniversityInfo] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const uniInfo = await loadUniversityInfo();
        setUniversityInfo(uniInfo);
        setLoading(false);
      } catch (error) {
        console.error('Error loading university info:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="about">
      {/* Hero Section */}
      <section className="about-hero">
        <div className="container">
          <h1>About {universityInfo.name}</h1>
          <p>Established in {universityInfo.established} • {universityInfo.location}</p>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="mission-vision section">
        <div className="container">
          <div className="grid grid-2">
            <div className="mission-card card">
              <h2>Our Mission</h2>
              <p>{universityInfo.mission}</p>
            </div>
            <div className="vision-card card">
              <h2>Our Vision</h2>
              <p>{universityInfo.vision}</p>
            </div>
          </div>
        </div>
      </section>

      {/* History Section */}
      <section className="history section">
        <div className="container">
          <div className="grid grid-2">
            <div className="history-content">
              <h2 className="section-title">Our History</h2>
              <p className="history-text">{universityInfo.history}</p>
              <p className="history-text">
                Over the decades, we have continuously evolved to meet the changing needs of our students 
                and society, while maintaining our commitment to academic excellence and innovation.
              </p>
            </div>
            <div className="history-image">
              <img src="/logo192.png" alt="University History" />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="values section">
        <div className="container">
          <h2 className="section-title">Our Core Values</h2>
          <div className="values-grid">
            {universityInfo.values?.map((value, index) => (
              <div key={index} className="value-item card">
                <div className="value-icon">
                  <i className="fas fa-star"></i>
                </div>
                <h3>{value}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="about-stats section">
        <div className="container">
          <h2 className="section-title">By the Numbers</h2>
          <div className="stats-grid">
            <div className="stat-card card">
              <div className="stat-number">{universityInfo.statistics?.students}</div>
              <div className="stat-label">Students Enrolled</div>
              <p>From undergraduate to doctoral programs</p>
            </div>
            <div className="stat-card card">
              <div className="stat-number">{universityInfo.statistics?.faculty}</div>
              <div className="stat-label">Faculty Members</div>
              <p>Dedicated educators and researchers</p>
            </div>
            <div className="stat-card card">
              <div className="stat-number">{universityInfo.statistics?.programs}</div>
              <div className="stat-label">Academic Programs</div>
              <p>Across multiple disciplines and levels</p>
            </div>
            <div className="stat-card card">
              <div className="stat-number">{universityInfo.statistics?.countries}</div>
              <div className="stat-label">Countries Represented</div>
              <p>Creating a diverse global community</p>
            </div>
          </div>
        </div>
      </section>

      {/* Campus Gallery Section */}
      <section className="campus-gallery section">
        <div className="container">
          <h2 className="section-title">Virtual Campus Tour</h2>
          <p className="section-subtitle">
            Explore our beautiful campus and state-of-the-art facilities
          </p>
          <div className="gallery-grid">
            {universityInfo.campusImages?.map((image, index) => (
              <div key={index} className="gallery-item">
                <img 
                  src={image} 
                  alt={`Campus view ${index + 1}`}
                  onError={(e) => {
                    e.target.src = '/logo192.png';
                  }}
                />
                <div className="gallery-overlay">
                  <h4>Campus Facility</h4>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="contact-info section">
        <div className="container">
          <div className="contact-grid">
            <div className="contact-item card">
              <div className="contact-icon">
                <i className="fas fa-map-marker-alt"></i>
              </div>
              <h3>Visit Us</h3>
              <p>{universityInfo.contact?.address}</p>
            </div>
            <div className="contact-item card">
              <div className="contact-icon">
                <i className="fas fa-phone"></i>
              </div>
              <h3>Call Us</h3>
              <p>{universityInfo.contact?.phone}</p>
            </div>
            <div className="contact-item card">
              <div className="contact-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <h3>Email Us</h3>
              <p>{universityInfo.contact?.email}</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
